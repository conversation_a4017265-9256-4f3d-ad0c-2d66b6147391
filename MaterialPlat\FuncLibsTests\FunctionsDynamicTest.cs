using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using System.Globalization;
using FuncLibs;
using NUnit.Framework;
using TestExpert.PlaneStrainFractureToughnessMethod.Commons;
using CsvHelper;
using CsvHelper.Configuration;

namespace FuncLibsTests
{
    public class FunctionsDynamicTest
    {
        [Test]
        public void collectValueBetweenPeekAndValleyTest()
        {
            double[] fuheArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] bianXingArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] expectedFuhe = { 20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] expectedBianXing = { 20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] actualFuhe;
            double[] actualBianXing;
            int result = FunctionsDynamic.collectValueBetweenPeekAndValley(0, fuheArray, bianXingArray, out actualFuhe, out actualBianXing);
            Assert.AreEqual(expectedFuhe, actualFuhe);
            Assert.AreEqual(expectedBianXing, actualBianXing);
            TestContext.WriteLine($"测试通过下降: collectValueBetweenPeekAndValleyTest{expectedFuhe.Length}个元素, 结果: {result}");
        }
        [Test]
        public void collectValueBetweenPeekAndValleyTest2()
        {
            double[] fuheArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] bianXingArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] expectedFuhe = { -10, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20 };
            double[] expectedBianXing = { -10, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20 };
            double[] actualFuhe;
            double[] actualBianXing;
            int result = FunctionsDynamic.collectValueBetweenPeekAndValley(1, fuheArray, bianXingArray, out actualFuhe, out actualBianXing);
            Assert.AreEqual(expectedFuhe, actualFuhe);
            Assert.AreEqual(expectedBianXing, actualBianXing);
            TestContext.WriteLine($"测试通过上升: collectValueBetweenPeekAndValleyTest{expectedFuhe.Length}个元素, 结果: {result}");
        }

        /// <summary>
        /// 测试collectValueBetweenPeekAndValley方法，从CSV文件读取数据并写入CSV文件
        /// sampleType=0 (紧凑型试样)
        /// </summary>
        [Test]
        public void CollectValueBetweenPeekAndValley_FromCsvFile_SampleType0()
        {
            // 准备测试数据文件路径
            string testDataDir = Path.Combine(TestContext.CurrentContext.TestDirectory, "TestData");
            Directory.CreateDirectory(testDataDir);

            string inputCsvPath = Path.Combine(testDataDir, "input_data_sampletype0.csv");
            string outputCsvPath = Path.Combine(testDataDir, "output_data_sampletype0.csv");

            try
            {
                // 创建输入CSV文件
                CreateInputCsvFile(inputCsvPath);

                // 从CSV文件读取数据
                var (fuheArray, bianXingArray) = ReadDataFromCsv(inputCsvPath);

                // 执行collectValueBetweenPeekAndValley方法，sampleType=0
                int result = FunctionsDynamic.collectValueBetweenPeekAndValley(
                    0, // sampleType=0 紧凑型试样
                    fuheArray,
                    bianXingArray,
                    out double[] out_fuheArray,
                    out double[] out_bianXiangArray,0.8,0.8
                );

                // 验证结果
                Assert.AreEqual(0, result, "方法应该成功执行并返回0");
                Assert.IsNotNull(out_fuheArray, "输出的负荷数组不应为null");
                Assert.IsNotNull(out_bianXiangArray, "输出的变形数组不应为null");
                Assert.AreEqual(out_fuheArray.Length, out_bianXiangArray.Length, "输出数组长度应该相等");
                Assert.Greater(out_fuheArray.Length, 0, "输出数组应该包含数据");

                // 将结果写入CSV文件
                WriteDataToCsv(outputCsvPath, out_fuheArray, out_bianXiangArray);

                // 验证输出文件是否创建成功
                Assert.IsTrue(File.Exists(outputCsvPath), "输出CSV文件应该被创建");

                // 验证输出文件内容
                var (outputFuhe, outputBianXing) = ReadDataFromCsv(outputCsvPath);
                Assert.AreEqual(out_fuheArray.Length, outputFuhe.Length, "输出文件中的负荷数据长度应该正确");
                Assert.AreEqual(out_bianXiangArray.Length, outputBianXing.Length, "输出文件中的变形数据长度应该正确");

                // 验证数据一致性
                for (int i = 0; i < out_fuheArray.Length; i++)
                {
                    Assert.AreEqual(out_fuheArray[i], outputFuhe[i], 1e-6, $"负荷数据第{i}个元素应该一致");
                    Assert.AreEqual(out_bianXiangArray[i], outputBianXing[i], 1e-6, $"变形数据第{i}个元素应该一致");
                }

                TestContext.WriteLine($"测试通过: sampleType=0, 输入数据长度: {fuheArray.Length}, 输出数据长度: {out_fuheArray.Length}");
                TestContext.WriteLine($"输入文件: {inputCsvPath}");
                TestContext.WriteLine($"输出文件: {outputCsvPath}");
            }
            finally
            {
                // 清理测试文件
                //if (File.Exists(inputCsvPath)) File.Delete(inputCsvPath);
                //if (File.Exists(outputCsvPath)) File.Delete(outputCsvPath);
            }
        }

        /// <summary>
        /// 创建输入CSV文件，包含测试数据
        /// </summary>
        private void CreateInputCsvFile(string filePath)
        {
            // 创建测试数据：模拟紧凑型试样的负荷-变形曲线
            // 先上升到峰值，再下降到谷值
            double[] fuheArray = {
                0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10,  // 初始下降段
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,  // 上升到峰值
                19, 18, 17, 16, 15, 14, 13, 12, 11, 10,  // 从峰值下降
                9, 8, 7, 6, 5, 4, 3, 2, 1, 0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10  // 继续下降到谷值
            };

            double[] bianXingArray = {
                0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10,  // 对应的变形数据
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                19, 18, 17, 16, 15, 14, 13, 12, 11, 10,
                9, 8, 7, 6, 5, 4, 3, 2, 1, 0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10
            };

            using var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

            // 写入表头
            csv.WriteField("负荷");
            csv.WriteField("变形");
            csv.NextRecord();

            // 写入数据
            for (int i = 0; i < fuheArray.Length; i++)
            {
                csv.WriteField(fuheArray[i]);
                csv.WriteField(bianXingArray[i]);
                csv.NextRecord();
            }
        }

        /// <summary>
        /// 从CSV文件读取数据
        /// </summary>
        private (double[] fuheArray, double[] bianXingArray) ReadDataFromCsv(string filePath)
        {
            var fuheList = new List<double>();
            var bianXingList = new List<double>();

            using var reader = new StreamReader(filePath, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);

            // 跳过表头
            csv.Read();
            csv.ReadHeader();

            // 读取数据
            while (csv.Read())
            {
                if (csv.TryGetField<double>(0, out double fuhe) &&
                    csv.TryGetField<double>(1, out double bianXing)&&csv.TryGetField<double>(2, out double cycle))
                {
                    if (cycle == 2223)
                    {
                        fuheList.Add(fuhe);
                        bianXingList.Add(bianXing);
                    }
                }
            }

            return (fuheList.ToArray(), bianXingList.ToArray());
        }

        /// <summary>
        /// 将数据写入CSV文件
        /// </summary>
        private void WriteDataToCsv(string filePath, double[] fuheArray, double[] bianXingArray)
        {
            using var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

            // 写入表头
            csv.WriteField("处理后负荷");
            csv.WriteField("处理后变形");
            csv.NextRecord();

            // 写入数据
            for (int i = 0; i < fuheArray.Length; i++)
            {
                csv.WriteField(fuheArray[i]);
                csv.WriteField(bianXingArray[i]);
                csv.NextRecord();
            }
        }

    }
}