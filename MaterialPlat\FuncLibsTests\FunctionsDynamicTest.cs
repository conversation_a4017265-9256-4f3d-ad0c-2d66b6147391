using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FuncLibs;
using NUnit.Framework;
using TestExpert.PlaneStrainFractureToughnessMethod.Commons;

namespace FuncLibsTests
{
    public class FunctionsDynamicTest
    {
        [Test]
        public void collectValueBetweenPeekAndValleyTest()
        {
            double[] fuheArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] bianXingArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] expectedFuhe = { 20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] expectedBianXing = { 20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] actualFuhe;
            double[] actualBianXing;
            int result = FunctionsDynamic.collectValueBetweenPeekAndValley(0, fuheArray, bianXingArray, out actualFuhe, out actualBianXing);
            Assert.AreEqual(expectedFuhe, actualFuhe);
            Assert.AreEqual(expectedBianXing, actualBianXing);
            TestContext.WriteLine($"测试通过下降: collectValueBetweenPeekAndValleyTest{expectedFuhe.Length}个元素, 结果: {result}");
        }
        [Test]
        public void collectValueBetweenPeekAndValleyTest2()
        {
            double[] fuheArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] bianXingArray = { 0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,
                                   19,18,17,16,15,14,13,12,11,10,
                                   9,8,7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10 };
            double[] expectedFuhe = { -10, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20 };
            double[] expectedBianXing = { -10, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20 };
            double[] actualFuhe;
            double[] actualBianXing;
            int result = FunctionsDynamic.collectValueBetweenPeekAndValley(1, fuheArray, bianXingArray, out actualFuhe, out actualBianXing);
            Assert.AreEqual(expectedFuhe, actualFuhe);
            Assert.AreEqual(expectedBianXing, actualBianXing);
            TestContext.WriteLine($"测试通过上升: collectValueBetweenPeekAndValleyTest{expectedFuhe.Length}个元素, 结果: {result}");
        }
        
    }
}