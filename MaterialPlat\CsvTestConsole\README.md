# CSV数据分析工具

## 项目概述

CSV数据分析工具是一个基于.NET 6开发的控制台应用程序，专门用于分析CSV文件中的数据模式。该工具能够自动检测第一列数值的连续性（相邻差值是否均为1）以及计算第二列数值的差值范围统计。

## 功能特性

- ✅ **多编码支持**：自动检测并支持UTF-8、GB2312、GBK等编码格式
- ✅ **第一列连续性检查**：验证相邻行数值差值是否均为1，识别异常行
- ✅ **第二列差值统计**：计算相邻行数值差值的最小值、最大值和平均值（原始差值，非绝对值）
- ✅ **原始数据追踪**：显示最小差值和最大差值对应的具体原始数据行
- ✅ **差值区间统计**：统计差值绝对值在不同区间内的记录行数，包括≤0.01、0.01~0.02、0.02~0.03、>0.03四个区间
- ✅ **异常数据处理**：自动跳过无效数据行，继续处理有效数据
- ✅ **详细结果报告**：提供总行数、有效行数、异常信息等统计数据
- ✅ **完整单元测试**：包含11个测试用例，覆盖各种场景

## 系统要求

### 运行环境
- **.NET 6.0 Runtime** 或更高版本
- **操作系统**：Windows 10/11, Linux, macOS
- **内存**：建议至少512MB可用内存
- **磁盘空间**：至少50MB可用空间

### 开发环境（如需修改代码）
- **.NET 6.0 SDK** 或更高版本
- **IDE**：Visual Studio 2022, Visual Studio Code, JetBrains Rider等
- **Git**（可选）：用于版本控制

## 安装步骤

### 1. 安装.NET 6.0 SDK

#### Windows
```bash
# 下载并安装.NET 6.0 SDK
# 访问：https://dotnet.microsoft.com/download/dotnet/6.0
# 或使用包管理器
winget install Microsoft.DotNet.SDK.6
```

#### Linux (Ubuntu/Debian)
```bash
# 添加Microsoft包源
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb

# 安装.NET 6.0 SDK
sudo apt-get update
sudo apt-get install -y dotnet-sdk-6.0
```

#### macOS
```bash
# 使用Homebrew
brew install --cask dotnet-sdk
```

### 2. 验证安装
```bash
dotnet --version
# 应显示6.0.x或更高版本
```

### 3. 获取项目代码
```bash
# 如果使用Git
git clone <repository-url>
cd CsvTestConsole

# 或直接下载项目文件到本地目录
```

### 4. 还原依赖包
```bash
cd CsvTestConsole
dotnet restore
```

### 5. 编译项目
```bash
# 编译主项目
dotnet build CsvAnalyzer.csproj

# 编译测试项目
dotnet build CsvAnalyzer.Tests.csproj
```

## 配置要求

### 项目依赖包

项目使用以下NuGet包：

```xml
<!-- 主项目依赖 -->
<PackageReference Include="CsvHelper" Version="30.0.1" />
<PackageReference Include="System.Text.Encoding.CodePages" Version="6.0.0" />

<!-- 测试项目依赖 -->
<PackageReference Include="NUnit" Version="3.13.3" />
<PackageReference Include="NUnit3TestAdapter" Version="4.2.1" />
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.3.2" />
```

### CSV文件格式要求

- **文件编码**：UTF-8、GB2312、GBK（工具会自动检测）
- **分隔符**：逗号（,）
- **文件结构**：
  ```
  序号,裂纹长度
  1,25.125
  2,25.136
  3,25.134
  ...
  ```
- **数据类型**：
  - 第一列：整数（序号）
  - 第二列：浮点数（测量值）

## 使用方法

### 基本使用

#### 1. 准备CSV文件
- 将要分析的CSV文件放入项目根目录下的 `TestData` 文件夹中
- 默认分析文件名为 `sample_data.csv`
- 如需分析其他文件，请修改 `Program.cs` 中的文件路径配置

#### 2. 运行程序
```bash
# 在项目目录下执行
dotnet run --project CsvAnalyzer.csproj
```

#### 3. 程序执行流程
1. 程序启动后会自动查找默认CSV文件
2. 如果找到文件，自动开始分析
3. 显示分析结果
4. 按任意键退出

#### 4. 自定义CSV文件路径
修改 `Program.cs` 中的文件路径：
```csharp
// 使用相对路径（推荐）
string csvFilePath = Path.Combine("TestData", "your_file.csv");

// 或使用绝对路径
string csvFilePath = @"C:\完整路径\your_file.csv";
```

### 运行测试

```bash
# 运行所有单元测试
dotnet test CsvAnalyzer.Tests.csproj

# 运行测试并显示详细输出
dotnet test CsvAnalyzer.Tests.csproj --verbosity normal

# 运行特定测试
dotnet test CsvAnalyzer.Tests.csproj --filter "TestMethodName"
```

### 输出结果说明

程序运行后会显示以下信息：

```
=== CSV文件分析结果 ===
文件路径: [文件完整路径]
总行数: [包含标题行的总行数]
有效数据行数: [有效的数据行数]

=== 第一列相邻数值差值分析 ===
✓ 第一列相邻数值的差值均为1
或
✗ 发现异常：第X行到第Y行的差值为Z

=== 第二列相邻数值差值范围分析 ===
注意：差值计算为原始差值（非绝对值），可能为正数或负数

最小差值: [最小差值]
  对应数据: 第X行 [前一个值] -> [当前值]

最大差值: [最大差值]
  对应数据: 第Y行 [前一个值] -> [当前值]

平均差值: [平均差值]
差值范围: [最小值, 最大值]

=== 差值绝对值区间统计 ===
差值绝对值 ≤ 0.01: [行数] 行
差值绝对值 0.01 ~ 0.02: [行数] 行
差值绝对值 0.02 ~ 0.03: [行数] 行
差值绝对值 > 0.03: [行数] 行
总差值记录行数: [总行数] 行

=== 分析完成 ===
```

## 常见问题及解决方案

### Q1: 差值计算是使用绝对值吗？
**答案**：不是。程序计算的是原始差值（当前值 - 前一个值），可能为正数或负数。
- 正数表示数值增加
- 负数表示数值减少
- 程序会显示最小差值和最大差值对应的具体原始数据行，便于分析数据变化趋势

### Q2: 运行时提示"找不到指定的文件"
**原因**：CSV文件路径不正确或文件不存在

**解决方案**：
1. 检查文件路径是否正确
2. 确认文件确实存在
3. 检查文件权限是否允许读取

### Q2: 差值区间统计是如何划分的？

**答案**：差值区间统计基于差值的绝对值进行划分：
- ≤ 0.01：差值绝对值小于等于0.01的记录
- 0.01 ~ 0.02：差值绝对值大于0.01且小于等于0.02的记录
- 0.02 ~ 0.03：差值绝对值大于0.02且小于等于0.03的记录
- > 0.03：差值绝对值大于0.03的记录

这种统计有助于了解数据波动的分布情况，识别数据的稳定性。

### Q3: 编码错误，中文显示乱码
**原因**：CSV文件使用了不支持的编码格式

**解决方案**：
1. 程序已自动支持UTF-8、GB2312、GBK编码
2. 如需支持其他编码，可在代码中添加相应的编码检测

### Q4: 编译时提示包依赖错误
**原因**：NuGet包未正确还原或版本冲突

**解决方案**：
```bash
# 清理并重新还原
dotnet clean
dotnet restore
dotnet build
```

### Q4: 测试运行失败
**原因**：测试环境配置问题或代码修改导致测试不通过

**解决方案**：
1. 检查测试项目是否正确引用主项目
2. 确认所有依赖包已正确安装
3. 查看具体的测试失败信息进行调试

### Q5: 程序运行缓慢
**原因**：处理大文件时可能出现性能问题

**解决方案**：
1. 确保有足够的可用内存
2. 对于超大文件，考虑分批处理
3. 检查磁盘I/O性能

### Q6: 多个项目文件导致运行错误
**原因**：目录中包含多个.csproj文件

**解决方案**：
```bash
# 明确指定要运行的项目
dotnet run --project CsvAnalyzer.csproj

# 或者运行测试项目
dotnet test CsvAnalyzer.Tests.csproj
```

## 注意事项和最佳实践

### 文件处理注意事项

1. **文件大小限制**：
   - 建议单个CSV文件不超过100MB
   - 对于更大的文件，考虑分割处理

2. **数据格式要求**：
   - 确保第一列为连续的整数序号
   - 第二列应为有效的数值数据
   - 避免在数据中包含特殊字符

3. **编码处理**：
   - 优先使用UTF-8编码保存CSV文件
   - 避免混合使用不同编码格式

### 开发最佳实践

1. **代码修改**：
   - 修改代码后务必运行单元测试
   - 遵循现有的代码风格和命名规范
   - 添加适当的注释和文档

2. **测试策略**：
   - 每次修改后运行完整的测试套件
   - 添加新功能时同步添加相应的测试用例
   - 保持测试覆盖率在90%以上

3. **性能优化**：
   - 对于大文件处理，考虑使用流式读取
   - 避免一次性加载整个文件到内存
   - 适当使用缓存机制

4. **错误处理**：
   - 始终包含适当的异常处理
   - 提供有意义的错误消息
   - 记录关键操作的日志信息

### 部署建议

1. **生产环境**：
   - 使用Release配置编译
   - 确保目标环境已安装.NET 6.0 Runtime
   - 配置适当的文件访问权限

2. **版本管理**：
   - 使用语义化版本号
   - 维护详细的变更日志
   - 定期备份重要的CSV数据文件

## 项目结构

```
CsvTestConsole/
├── CsvAnalyzer.csproj          # 主项目文件
├── CsvAnalyzer.Tests.csproj    # 测试项目文件
├── Program.cs                  # 程序入口点
├── CsvDataAnalyzer.cs         # 核心分析逻辑
├── CsvAnalyzerTests.cs        # 单元测试用例
├── README.md                  # 项目文档（本文件）
├── bin/                       # 编译输出目录
└── obj/                       # 编译临时文件目录
```

## 技术支持

如遇到问题或需要技术支持，请：

1. 首先查阅本文档的常见问题部分
2. 检查项目的单元测试是否通过
3. 查看详细的错误日志信息
4. 联系开发团队获取进一步支持

## 版本历史

- **v1.0.0** (2024-01-09)
  - 初始版本发布
  - 实现基本的CSV分析功能
  - 添加完整的单元测试套件
  - 支持多种编码格式

---

*最后更新：2024年1月9日*