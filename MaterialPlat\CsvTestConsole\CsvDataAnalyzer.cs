using CsvHelper;
using System.Globalization;
using System.Text;

namespace CsvAnalyzer;

/// <summary>
/// CSV数据分析器
/// </summary>
public class CsvDataAnalyzer
{
    static CsvDataAnalyzer()
    {
        // 注册编码提供程序以支持GB2312等编码
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
    }
    /// <summary>
    /// 分析CSV文件
    /// </summary>
    /// <param name="filePath">CSV文件路径</param>
    /// <returns>分析结果</returns>
    public CsvAnalysisResult AnalyzeCsvFile(string filePath)
    {
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"文件不存在: {filePath}");
        }

        var result = new CsvAnalysisResult();
        var firstColumnValues = new List<int>();
        var secondColumnValues = new List<double>();

        // 尝试不同的编码方式读取文件
        var encodings = new[] { Encoding.UTF8, Encoding.GetEncoding("GB2312"), Encoding.Default };
        
        foreach (var encoding in encodings)
        {
            try
            {
                using var reader = new StringReader(File.ReadAllText(filePath, encoding));
                using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
                
                csv.Read();
                csv.ReadHeader();
                result.TotalRows++;

                while (csv.Read())
                {
                    result.TotalRows++;
                    
                    try
                    {
                        // 读取第一列（序号）
                        if (csv.TryGetField<int>(0, out int firstValue))
                        {
                            firstColumnValues.Add(firstValue);
                        }
                        else
                        {
                            continue; // 跳过无效行
                        }

                        // 读取第二列（裂纹长度）
                        if (csv.TryGetField<double>(1, out double secondValue))
                        {
                            secondColumnValues.Add(secondValue);
                            result.ValidRows++;
                        }
                        else
                        {
                            firstColumnValues.RemoveAt(firstColumnValues.Count - 1); // 移除对应的第一列值
                        }
                    }
                    catch
                    {
                        // 跳过无法解析的行
                        continue;
                    }
                }
                break; // 成功读取，跳出编码循环
            }
            catch
            {
                // 尝试下一种编码
                continue;
            }
        }

        if (result.ValidRows == 0)
        {
            throw new InvalidDataException("CSV文件中没有有效的数据行");
        }

        // 分析第一列相邻数值差值
        AnalyzeFirstColumnDifferences(firstColumnValues, result);

        // 分析第二列相邻数值差值范围
        AnalyzeSecondColumnDifferences(secondColumnValues, result);

        return result;
    }

    /// <summary>
    /// 分析第一列相邻数值差值
    /// </summary>
    private void AnalyzeFirstColumnDifferences(List<int> values, CsvAnalysisResult result)
    {
        if (values.Count < 2)
        {
            result.FirstColumnDifferencesAllOne = true;
            return;
        }

        bool allDifferencesAreOne = true;
        
        for (int i = 1; i < values.Count; i++)
        {
            int difference = values[i] - values[i - 1];
            
            if (difference != 1)
            {
                allDifferencesAreOne = false;
                result.FirstColumnAnomalies.Add(new ColumnAnomaly
                {
                    RowIndex = i + 1, // +1 因为包含标题行
                    PreviousValue = values[i - 1],
                    CurrentValue = values[i],
                    Difference = difference
                });
            }
        }

        result.FirstColumnDifferencesAllOne = allDifferencesAreOne;
    }

    /// <summary>
    /// 分析第二列相邻数值差值范围
    /// </summary>
    private void AnalyzeSecondColumnDifferences(List<double> values, CsvAnalysisResult result)
    {
        if (values.Count < 2)
        {
            return;
        }

        var differences = new List<double>();
        var differenceDetails = new List<DifferenceDetail>();
        
        for (int i = 1; i < values.Count; i++)
        {
            double difference = values[i] - values[i - 1];
            differences.Add(difference);
            
            differenceDetails.Add(new DifferenceDetail
            {
                RowIndex = i + 1, // +1 因为不包含标题行，从第2行数据开始
                PreviousValue = values[i - 1],
                CurrentValue = values[i],
                Difference = difference
            });
        }

        if (differences.Count > 0)
        {
            result.SecondColumnMinDifference = differences.Min();
            result.SecondColumnMaxDifference = differences.Max();
            result.SecondColumnAverageDifference = differences.Average();
            
            // 找到最小差值和最大差值对应的详细信息
            result.MinDifferenceDetail = differenceDetails.First(d => Math.Abs(d.Difference - result.SecondColumnMinDifference) < 1e-10);
            result.MaxDifferenceDetail = differenceDetails.First(d => Math.Abs(d.Difference - result.SecondColumnMaxDifference) < 1e-10);
            
            // 统计不同区间的差值记录行数
            foreach (var difference in differences)
            {
                double absDifference = Math.Abs(difference);
                
                if (absDifference <= 0.01)
                {
                    result.DifferenceWithin001Count++;
                }
                else if (absDifference <= 0.02)
                {
                    result.DifferenceBetween001And002Count++;
                }
                else if (absDifference <= 0.03)
                {
                    result.DifferenceBetween002And003Count++;
                }
                else
                {
                    result.DifferenceAbove003Count++;
                }
            }
        }
    }
}

/// <summary>
/// CSV分析结果
/// </summary>
public class CsvAnalysisResult
{
    /// <summary>
    /// 总行数（包括标题行）
    /// </summary>
    public int TotalRows { get; set; }

    /// <summary>
    /// 有效数据行数
    /// </summary>
    public int ValidRows { get; set; }

    /// <summary>
    /// 第一列相邻数值差值是否均为1
    /// </summary>
    public bool FirstColumnDifferencesAllOne { get; set; }

    /// <summary>
    /// 第一列异常情况列表
    /// </summary>
    public List<ColumnAnomaly> FirstColumnAnomalies { get; set; } = new List<ColumnAnomaly>();

    /// <summary>
    /// 第二列相邻数值差值的最小值
    /// </summary>
    public double SecondColumnMinDifference { get; set; }

    /// <summary>
    /// 第二列相邻数值差值的最大值
    /// </summary>
    public double SecondColumnMaxDifference { get; set; }

    /// <summary>
    /// 第二列相邻数值差值的平均值
    /// </summary>
    public double SecondColumnAverageDifference { get; set; }

    /// <summary>
    /// 最小差值对应的原始数据信息
    /// </summary>
    public DifferenceDetail MinDifferenceDetail { get; set; }

    /// <summary>
    /// 最大差值对应的原始数据信息
    /// </summary>
    public DifferenceDetail MaxDifferenceDetail { get; set; }

    /// <summary>
    /// 差值绝对值在0.01以内的记录行数
    /// </summary>
    public int DifferenceWithin001Count { get; set; }

    /// <summary>
    /// 差值绝对值在0.01~0.02之间的记录行数
    /// </summary>
    public int DifferenceBetween001And002Count { get; set; }

    /// <summary>
    /// 差值绝对值在0.02~0.03之间的记录行数
    /// </summary>
    public int DifferenceBetween002And003Count { get; set; }

    /// <summary>
    /// 差值绝对值大于0.03的记录行数
    /// </summary>
    public int DifferenceAbove003Count { get; set; }
}

/// <summary>
/// 列异常信息
/// </summary>
public class ColumnAnomaly
{
    /// <summary>
    /// 行索引
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    /// 前一个值
    /// </summary>
    public int PreviousValue { get; set; }

    /// <summary>
    /// 当前值
    /// </summary>
    public int CurrentValue { get; set; }

    /// <summary>
    /// 差值
    /// </summary>
    public int Difference { get; set; }
}

/// <summary>
/// 差值详细信息
/// </summary>
public class DifferenceDetail
{
    /// <summary>
    /// 行索引（数据行，不包含标题行）
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    /// 前一个值
    /// </summary>
    public double PreviousValue { get; set; }

    /// <summary>
    /// 当前值
    /// </summary>
    public double CurrentValue { get; set; }

    /// <summary>
    /// 差值（当前值 - 前一个值）
    /// </summary>
    public double Difference { get; set; }
}