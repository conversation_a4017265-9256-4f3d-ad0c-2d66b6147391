using System.Collections;
using System.Dynamic;
using System.Globalization;
using static Logging.CCSSLogger;
using CsvHelper;
using NPOI.SS.UserModel;
using NPOI.SS.UserModel.Charts;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using NPOI.XSSF.Streaming;
using NPOI.XSSF.UserModel.Charts;
using ScriptEngine.ResultVar;
using Scripting;
using static Scripting.ITemplate;
using ScriptEngine;
using ScriptEngine.InputVar.InputVars;
using TaskServer.ApiServer.Routes.Report.Models;

namespace TaskServer.ApiServer.Routes.Report;

/// <summary>
/// 报表中的单位参数
/// </summary>
/// <param name="Code">单位code</param>
/// <param name="Factor">单位转换因数</param>
public record ReportUnitParams(string Code, double Factor)
{
    /// <summary>
    /// int类型数值的单位换算
    /// </summary>
    /// <returns></returns>
    public static double Conversion(ReportUnitParams? reportUnitParams, int value)
    {
        if (reportUnitParams == null)
            return value;
        return value * reportUnitParams.Factor;
    }
    
    /// <summary>
    /// double类型数值的单位换算
    /// </summary>
    /// <returns></returns>
    public static double Conversion(ReportUnitParams? reportUnitParams, double value)
    {
        if (reportUnitParams == null)
            return value;
        return value * reportUnitParams.Factor;
    }
}

/// <summary>
/// 修约参数
/// </summary>
public record ReportRoundParams(int Threshold1, int Threshold2, short RoundMode, int RoundType1, int RoundType2, int RoundType3)
{
    /// <summary>
    /// 根据修约计算value的目标格式 
    /// </summary>
    public string RoundOff(double value)
    {
        return Round.RoundOff(value, RoundMode, Threshold1, Threshold2, RoundType1, RoundType2, RoundType3)
            .ToString(CultureInfo.InvariantCulture);
    }
}

/// <summary>
/// 报表中的格式参数
/// </summary>
/// <param name="ExponentialDecimalDigits">幂数型小数位数</param>
public record ReportFormatParams(
    int? ExponentialDecimalDigits = null,
    int? DecimalDigits = null,
    int? SignificantDigits =null,
    ReportRoundParams? RoundParams = null)
{
    /// <summary>
    /// 根据格式格式化数值
    /// </summary>
    public string Format(double value)
    {
        // 幂数型
        if (ExponentialDecimalDigits != null)
        {
            return value.ToString($"e{ExponentialDecimalDigits}", CultureInfo.InvariantCulture);
        }
        // 自定义
        if (DecimalDigits != null)
        {
            return value.ToString($"F{DecimalDigits}", CultureInfo.InvariantCulture);
        }
        // 有效数字
        if (SignificantDigits != null)
        {
            return value.ToString($"g{SignificantDigits}", CultureInfo.InvariantCulture);
        }
        // 修约
        if (RoundParams != null)
        {
            return RoundParams.RoundOff(value);
        }
        // 默认
        return value.ToString(CultureInfo.InvariantCulture);
    }
    
    /// <summary>
    /// double类型数值的单位换算
    /// </summary>
    /// <returns></returns>
    public static double Conversion(ReportUnitParams? reportUnitParams, double value)
    {
        if (reportUnitParams == null)
            return value;
        return value * reportUnitParams.Factor;
    }
}

/// <summary>
/// 报表输入变量参数
/// </summary>
public record ReportInputVarParams(
    string Name,
    dynamic? Value,
    ReportUnitParams? Unit
    );

/// <summary>
/// 导出报表试样实例参数
/// </summary>
public record ReportSampleInstParams(
    string Name,
    string Code,
    List<ReportInputVarParams> InputVars
    );

/// <summary>
/// 导出报表结果变量参数
/// </summary>
public record ReportResultVarParams(
    string Code,
    ReportUnitParams? Unit,
    ReportFormatParams FormatInfo
    );

/// <summary>
/// 导出报表信号变量参数
/// </summary>
public record ReportSignalVarParams(
    string Name,
    string Code,
    ReportUnitParams? Unit
    );

/// <summary>
/// 导出历史数据参数
/// </summary>
/// <param name="XAxisCode">x轴信号变量code</param>
/// <param name="YAxisCode">y轴信号变量code</param>
/// <param name="BufferCode">历史数据保存buffer code</param>
/// <param name="SignalVars">信号变量参数</param>
public record ReportHistoricalDataParams(
    string? XAxisCode,
    string? YAxisCode,
    string? BufferCode,
    List<ReportSignalVarParams> SignalVars
    );

/// <summary>
/// 导出报表参数
/// </summary>
public record ExportReportParams(
    string TemplateName,
    string ExportReportFilePath,
    List<ReportSampleInstParams> SampleInsts,
    List<ReportResultVarParams> ResultVars,
    List<StatisticalPattern> ResultVarStatisticalPatterns,
    ReportHistoricalDataParams HistoricalDataParams
);
public record ExportDoubleArrayParams( string TemplateName,string ArrayCode,string Type, List<DoubleArrayExportParam> Codes, string Path, string FileName);

/// <summary>
/// 导出CSV参数 
/// </summary>
public record ExportCsvParams(
    string TemplateName,
    string ExportCsvFilePath,
    string ExportCsvFileName,
    List<string> SampleInstCodes,
    string BufferCode
);

/// <summary>
/// 初始化Excel导出参数
/// </summary>
public record InitExcelExportParams(
    string TemplateName,
    string ExportReportFilePath,
    List<ReportSampleInstParams> SampleInsts,
    List<ReportResultVarParams> ResultVars,
    List<StatisticalPattern> ResultVarStatisticalPatterns,
    ReportHistoricalDataParams HistoricalDataParams,
    int PageSize,
    int? HistoricalDataPageSize = null
);

/// <summary>
    /// 追加Excel数据参数
    /// </summary>
    public record AppendExcelDataParams(
        string ExportReportFilePath,
        List<string> SampleInstCodes,
        int PageIndex,
        int PageSize,
        List<ReportResultVarParams> ResultVars,
        List<StatisticalPattern> ResultVarStatisticalPatterns,
        string TemplateName,
        ReportHistoricalDataParams? HistoricalDataParams,
        string? TargetSampleCode,
        int? HistoricalDataPageIndex,
        int? HistoricalDataPageSize,
        List<ReportSampleInstParams>? SampleInsts
    );

/// <summary>
/// 完成Excel导出参数
/// </summary>
public record FinalizeExcelExportParams(
    string ExportReportFilePath
);

/// <summary>
/// 试样历史数据分页信息
/// </summary>
public record SampleHistoricalDataInfo(
    string SampleCode,
    string SampleName,
    int TotalDataRows,
    int TotalPages
);

/// <summary>
/// 初始化Excel导出结果
/// </summary>
public record InitExcelExportResult(
    int TotalTestResultPages,
    List<SampleHistoricalDataInfo> SampleHistoricalDataInfos,
    string ExportReportFilePath
);

/// <summary>
/// 导出报表Service
/// </summary>
public static class Service
{
    /// <summary>
    /// 导出报表
    /// </summary>
    public static void ReportExport(ExportReportParams param)
    {
        // 提前创建文件流, 在目标文件被占用时直接抛出异常, 不执行后续内容
        using var fs = new FileStream(param.ExportReportFilePath, FileMode.Create, FileAccess.Write);
        using IWorkbook workbook = new XSSFWorkbook();
        var template = ITemplate.GetTemplateByName(param.TemplateName);
        // 参数
        workbook.CreateSheet0(param.SampleInsts);
        if (template is { IsProject: true })
        {
            var dbResultVars = DbResultVar.GetDbResultVars(template,
                param.SampleInsts.Select(inst => inst.Code).ToArray(),
                param.ResultVars.Select(resultVar => resultVar.Code).ToArray());
            // 测试结果
            workbook.CreateSheet1(dbResultVars, param.ResultVars, param.SampleInsts);
            // 数据统计
            workbook.CreateSheet2(dbResultVars, param.ResultVars, param.SampleInsts, param.ResultVarStatisticalPatterns);
            // 试样历史数据
            workbook.CreateOtherSheets(template, param.SampleInsts, param.HistoricalDataParams);
        }
        // TODO: 分段处理
        workbook.Write(fs);
        GC.Collect();
        GC.WaitForPendingFinalizers();
    }

    /// <summary>
    /// 异步导出CSV报表
    /// </summary>
    /// <param name="param">导出参数</param>
    /// <param name="taskId">任务ID</param>
    public static async Task CsvExportAsync(ExportCsvParams param, string taskId)
    {
        try
        {
            // 更新任务状态为运行中
            TaskStatusManager.UpdateStatus(taskId, TaskStatusEnum.Running, 0, "开始导出CSV文件");

            var template = ITemplate.GetTemplateByName(param.TemplateName);
            if (template is { IsProject: false })
            {
                TaskStatusManager.UpdateStatus(taskId, TaskStatusEnum.Failed, 0, "模板不存在或不是项目", "模板无效");
                return;
            }

            const int maxRowsPerFile = 1_000_000;
            int totalFiles = 0;
            int completedFiles = 0;
            long totalRows = 0;
            long processedRows = 0;

            // 计算总文件数和总行数
            foreach (var sampleInstCode in param.SampleInstCodes)
            {
                var tableName = sampleInstCode + param.BufferCode;
                var rowCount = template.Db.GetHistoricalDataCount(tableName);
                totalRows += rowCount;
                totalFiles += (int)Math.Ceiling((double)rowCount / maxRowsPerFile);
            }

            // 更新总体信息
            TaskStatusManager.UpdateProgress(taskId, 0, 0, totalFiles, 0, totalRows);

            foreach (var sampleInstCode in param.SampleInstCodes)
            {
                var dynamicDataTable = template.Db.GetTable(sampleInstCode, param.BufferCode);
                var tableName = sampleInstCode + param.BufferCode;
                var sampleTotalRows = template.Db.GetHistoricalDataCount(tableName);
                var fields = dynamicDataTable.Fields.ToList();
                fields.Add("computer_time");
                int fileIndex = 1;
                int exportedRows = 0;
                while (exportedRows < sampleTotalRows)
                {
                    int rowsThisFile = Math.Min(maxRowsPerFile, sampleTotalRows - exportedRows);
                    string fileName = $"{param.ExportCsvFilePath}{param.ExportCsvFileName}{sampleInstCode}_{fileIndex}.csv";

                    // 异步写入文件
                    await WriteCSVFileAsync(fileName, fields.ToArray(), template, tableName, exportedRows, maxRowsPerFile);

                    exportedRows += rowsThisFile;
                    processedRows += rowsThisFile;
                    completedFiles++;

                    // 更新进度
                    var progress = totalRows > 0 ? (int)((processedRows * 100) / totalRows) : 0;
                    TaskStatusManager.UpdateProgress(taskId, progress, completedFiles, totalFiles, processedRows, totalRows);

                    fileIndex++;

                    // 添加小延迟，避免过度占用资源
                    await Task.Delay(10);
                }
            }

            // 完成导出
            TaskStatusManager.UpdateStatus(taskId, TaskStatusEnum.Completed, 100,
                $"CSV导出完成，共导出 {completedFiles} 个文件，{processedRows} 行数据");
        }
        catch (IOException ioEx) when (ioEx.Message.Contains("being used by another process"))
        { 
            TaskStatusManager.UpdateStatus(taskId, TaskStatusEnum.Failed, 0,
                "CSV导出失败", "文件正在被其他进程使用");
            // 记录详细错误日志
            Logger.Error($"CSV异步导出失败，任务ID: {taskId}, 错误: {ioEx}");
        }
        catch (Exception ex)
        {
            // 导出失败
            TaskStatusManager.UpdateStatus(taskId, TaskStatusEnum.Failed, 0,
                "CSV导出失败", ex.Message);

            // 记录详细错误日志
            Logger.Error($"CSV异步导出失败，任务ID: {taskId}, 错误: {ex}");
        }
    }

    /// <summary>
    /// 异步写入CSV文件
    /// </summary>
    private static async Task WriteCSVFileAsync(string fileName, string[] fields, ITemplate template,
        string tableName, int skipRows, int takeRows)
    {
        using var writer = new StreamWriter(fileName);
        using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

        // 写表头
        foreach (var field in fields)
        {
            if (field == "computer_time")
            {
                csv.WriteField("计算机时间");
            }
            else
            {
                csv.WriteField(template.SignalVars[field].Name);
            }
        }
        csv.NextRecord();

        // 分批读取数据
        var pagedData = template.Db.GetHistoricalDataPaged(tableName, fields, skipRows / 1_000_000, takeRows);
        int rowCount = 0;

        foreach (var data in pagedData)
        {
            for (int i = 0; i < fields.Length; i++)
            {
                csv.WriteField(data[i]);
            }
            csv.NextRecord();
            rowCount++;

            if (rowCount >= takeRows) break;

            // 每1000行异步让出控制权
            if (rowCount % 1000 == 0)
            {
                await Task.Yield();
            }
        }

        await writer.FlushAsync();
    }

    /// <summary>
    /// 导出CSV报表（同步版本，保持兼容性）
    /// </summary>
    /// <param name="param"></param>
    public static void CsvExport(ExportCsvParams param)
    {
        var template = ITemplate.GetTemplateByName(param.TemplateName);
        if (template is { IsProject: false }) return;
        const int maxRowsPerFile = 1_000_000;
        foreach (var sampleInstCode in param.SampleInstCodes)
        {
            var dynamicDataTable = template.Db.GetTable(sampleInstCode, param.BufferCode);
            var tableName = sampleInstCode + param.BufferCode;
            var totalRows = template.Db.GetHistoricalDataCount(tableName);
            var fields = dynamicDataTable.Fields;
            int fileIndex = 1;
            int exportedRows = 0;
            while (exportedRows < totalRows)
            {
                int rowsThisFile = Math.Min(maxRowsPerFile, totalRows - exportedRows);
                string fileName = $"{param.ExportCsvFilePath}{param.ExportCsvFileName}{sampleInstCode}_{fileIndex}.csv";
                using var writer = new StreamWriter(fileName);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
                // 写表头
                foreach (var field in fields)
                {
                    csv.WriteField(field);
                }
                csv.NextRecord();

                // 分批读取数据
                var pagedData = template.Db.GetHistoricalDataPaged(tableName, fields, exportedRows / maxRowsPerFile, maxRowsPerFile);
                int rowCount = 0;
                foreach (var data in pagedData)
                {
                    for (int i = 0; i < fields.Length; i++)
                    {
                        csv.WriteField(data[i]);
                    }
                    csv.NextRecord();
                    rowCount++;
                    if (rowCount >= rowsThisFile) break;
                }
                exportedRows += rowCount;
                fileIndex++;
            }
        }
    }

    // 静态字典存储文件流和工作簿，以文件路径为键
    private static readonly Dictionary<string, (FileStream FileStream, IWorkbook Workbook, List<SampleHistoricalDataInfo>? SampleHistoricalDataInfos, int HistoricalDataPageSize)> _openFiles = new();
    private static readonly object _fileLock = new object();

    /// <summary>
    /// 初始化Excel导出
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    public static InitExcelExportResult InitExcelExport(InitExcelExportParams param)
    {
        try
        {
            var template = ITemplate.GetTemplateByName(param.TemplateName);

            // 为文件名添加时间戳确保唯一性
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
            var fileInfo = new FileInfo(param.ExportReportFilePath);
            var directory = fileInfo.DirectoryName;
            var fileName = Path.GetFileNameWithoutExtension(fileInfo.Name);
            var extension = fileInfo.Extension;
            var uniqueFilePath = Path.Combine(directory, $"{fileName}_{timestamp}{extension}");

            lock (_fileLock)
            {
                try
                {
                    // 如果文件已经打开，先关闭
                    if (_openFiles.ContainsKey(uniqueFilePath))
                    {
                        var (oldFs, oldWorkbook, _, _) = _openFiles[uniqueFilePath];
                        try
                        {
                            oldWorkbook?.Close();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"关闭旧工作簿时出错: {ex.Message}");
                        }
                        try
                        {
                            oldFs?.Close();
                            oldFs?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"关闭旧文件流时出错: {ex.Message}");
                        }
                        _openFiles.Remove(uniqueFilePath);
                    }

                    // 确保目录存在
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // 创建新的文件流和工作簿
                    FileStream fs = null;
                    IWorkbook workbook = null;

                    try
                    {
                        // 创建一个新的工作簿实例
                        workbook = new SXSSFWorkbook(); // .xlsx, 使用SXSSFWorkbook处理大文件

                        // 创建文件流用于写入
                        // FileMode.Create 会覆盖已存在的文件，如果不存在则创建新文件。
                        // FileAccess.Write 允许写入操作。
                        fs = new FileStream(uniqueFilePath, FileMode.Create, FileAccess.Write, FileShare.None);

                        // 创建基本信息sheet
                        workbook.CreateSheet0(param.SampleInsts);

                        // 计算测试结果的总页数
                        var allSampleCodes = param.SampleInsts.Select(inst => inst.Code).Distinct().ToList();
                        var totalTestResultPages = (int)Math.Ceiling((double)allSampleCodes.Count / param.PageSize);

                        // 获取每个试样的历史数据分页信息
                        var sampleHistoricalDataInfos = new List<SampleHistoricalDataInfo>();

                        if (template is { IsProject: true })
                        {
                            // 创建测试结果和数据统计sheet的表头
                            workbook.CreateSheet1Header(param.ResultVars);
                            workbook.CreateSheet2Header(param.ResultVars, param.ResultVarStatisticalPatterns);

                            // 只创建历史数据sheet的表头，不加载数据
                            if (param.HistoricalDataParams.BufferCode != null)
                            {
                                foreach (var sampleInst in param.SampleInsts)
                                {
                                    var sqlTableName = sampleInst.Code + param.HistoricalDataParams.BufferCode;
                                    var totalDataRows = template.Db.GetHistoricalDataCount(sqlTableName);
                                    var historicalDataPageSize = param.HistoricalDataPageSize ?? 10000; // 历史数据每页大小，默认1000行
                                    var totalPages = (int)Math.Ceiling((double)totalDataRows / historicalDataPageSize);

                                    sampleHistoricalDataInfos.Add(new SampleHistoricalDataInfo(
                                        sampleInst.Code,
                                        sampleInst.Name,
                                        totalDataRows,
                                        totalPages
                                    ));

                                    // 创建历史数据sheet表头（不加载数据）
                                    workbook.CreateHistoricalDataSheetHeader(param.HistoricalDataParams, sampleInst.Name);
                                }
                            }
                        }

                        // 存储文件流、工作簿和历史数据信息供后续使用
                        _openFiles[uniqueFilePath] = (fs, workbook, sampleHistoricalDataInfos, param.HistoricalDataPageSize ?? 10000);

                        GC.Collect();
                        GC.WaitForPendingFinalizers();

                        return new InitExcelExportResult(totalTestResultPages, sampleHistoricalDataInfos, uniqueFilePath);
                    }
                    catch (Exception ex)
                    {
                        // 如果创建过程中出错，清理资源
                        try
                        {
                            workbook?.Close();
                        }
                        catch { }
                        try
                        {
                            fs?.Close();
                            fs?.Dispose();
                        }
                        catch { }

                        Logger.Error($"创建Excel文件时出错 - StackTrace: {ex.StackTrace}");
                        throw new Exception($"创建Excel文件时出错: {ex.Message}. 文件路径: {uniqueFilePath}. [CODE_VERSION:2024.12.08_V6_FIXED]", ex);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"初始化Excel导出时出错 - StackTrace: {ex.StackTrace}");
                    throw new Exception($"初始化Excel导出时出错: {ex.Message}. 原始文件路径: {param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V6_FIXED]", ex);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.Error($"InitExcelExport方法执行失败 - StackTrace: {ex.StackTrace}");
            throw new Exception($"InitExcelExport方法执行失败: {ex.Message}. 参数: TemplateName={param.TemplateName}, ExportReportFilePath={param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V6_FIXED]", ex);
        }
    }

    /// <summary>
    /// 追加Excel数据
    /// </summary>
    /// <param name="param"></param>
    public static void AppendExcelData(AppendExcelDataParams param)
    {
        try
        {
            var template = ITemplate.GetTemplateByName(param.TemplateName);

            if (template is { IsProject: true })
            {
                lock (_fileLock)
                {
                    try
                    {
                        // 获取已打开的文件流和工作簿
                        if (!_openFiles.TryGetValue(param.ExportReportFilePath, out var fileInfo))
                        {
                            // 尝试查找带时间戳的文件
                            var matchingFile = _openFiles.Keys.FirstOrDefault(key =>
                                key.Contains(Path.GetFileNameWithoutExtension(param.ExportReportFilePath)));

                            if (matchingFile != null)
                            {
                                fileInfo = _openFiles[matchingFile];
                            }
                            else
                            {
                                throw new InvalidOperationException($"文件 {param.ExportReportFilePath} 未初始化，请先调用 InitExcelExport。当前打开的文件: {string.Join(", ", _openFiles.Keys)}");
                            }
                        }

                        var (fs, workbook, _, historicalDataPageSize) = fileInfo;

                        // 检查文件流和工作簿是否有效
                        if (fs == null || fs.CanWrite == false)
                        {
                            throw new ObjectDisposedException("FileStream", "文件流已被释放或无法写入");
                        }

                        if (workbook == null)
                        {
                            throw new ObjectDisposedException("IWorkbook", "工作簿已被释放");
                        }

                        // 如果是历史数据分页请求
                        if (!string.IsNullOrEmpty(param.TargetSampleCode) && param.HistoricalDataParams != null)
                        {
                            // 追加历史数据
                            AppendHistoricalData(workbook, template, param, historicalDataPageSize);
                        }
                        else
                        {
                            // 获取当前页的样本代码
                            var startIndex = param.PageIndex * param.PageSize;
                            var endIndex = Math.Min(startIndex + param.PageSize, param.SampleInstCodes.Count);
                            var currentPageSampleCodes = param.SampleInstCodes.Skip(startIndex).Take(endIndex - startIndex).ToList();

                            // 获取当前页的试样实例信息
                            var currentPageSampleInsts = param.SampleInsts?.Where(s => currentPageSampleCodes.Contains(s.Code)).ToList() ?? new List<ReportSampleInstParams>();

                            // 追加数据到相应的sheet
                            workbook.AppendDataToSheets(currentPageSampleCodes, currentPageSampleInsts, param.PageIndex, param.ResultVars, param.ResultVarStatisticalPatterns, param.TemplateName);
                        }

                        // 数据已添加到工作簿，将在 FinalizeExcelExport 中统一写入文件
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"追加Excel数据时出错 - StackTrace: {ex.StackTrace}");
                        throw new Exception($"追加Excel数据时出错: {ex.Message}. 文件路径: {param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V7_FIXED]", ex);
                    }
                }
            }

            GC.Collect();
            GC.WaitForPendingFinalizers();
        }
        catch (Exception ex)
        {
            Logger.Error($"AppendExcelData方法执行失败 - StackTrace: {ex.StackTrace}");
            throw new Exception($"AppendExcelData方法执行失败: {ex.Message}. 参数: TemplateName={param.TemplateName}, ExportReportFilePath={param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V7_FIXED]", ex);
        }
    }

    /// <summary>
    /// 追加历史数据到指定试样的sheet
    /// </summary>
    private static void AppendHistoricalData(IWorkbook workbook, ITemplate template, AppendExcelDataParams param, int historicalDataPageSize)
    {
        var sqlTableName = param.TargetSampleCode + param.HistoricalDataParams.BufferCode;

        List<ReportSignalVarParams> signalVars;
        if (string.IsNullOrEmpty(param.HistoricalDataParams.XAxisCode) ||
            string.IsNullOrEmpty(param.HistoricalDataParams.YAxisCode))
        {

            signalVars = param.HistoricalDataParams.SignalVars;
        }
        // 有xy轴code时 需要生成折线图 x轴数据 => 列0, y轴数据 => 列1
        else
        {
            signalVars = new List<ReportSignalVarParams>();
            foreach (var signalVar in param.HistoricalDataParams.SignalVars)
            {
                // x轴 => 列0
                if (signalVar.Code == param.HistoricalDataParams.XAxisCode)
                    signalVars.Insert(0, signalVar);
                // y轴 => 列1(当判断的x轴未插入时y轴放在列0)
                else if (signalVar.Code == param.HistoricalDataParams.YAxisCode)
                    signalVars.Insert(signalVars.Count > 0 && signalVars[0].Code == param.HistoricalDataParams.XAxisCode ? 1 : 0, signalVar);
                // 默认信号变量放在列表最后
                else
                    signalVars.Add(signalVar);
            }
        }
        var signalCodes = signalVars.Select(v => v.Code).ToArray();
        // 分页获取历史数据
        var historicalData = template.Db.GetHistoricalDataPaged(
            sqlTableName,
            signalCodes,
            param.HistoricalDataPageIndex ?? 0,
            historicalDataPageSize
        ).ToList();

        // 找到对应的sheet
        var targetSampleInst = param.SampleInsts?.FirstOrDefault(s => s.Code == param.TargetSampleCode);
        if (targetSampleInst == null) return;

        var sheet = workbook.GetSheet(targetSampleInst.Name);
        if (sheet == null) return;

        // 计算起始行号（跳过表头的3行）
        var startRowNum = 3 + ((param.HistoricalDataPageIndex ?? 0) * historicalDataPageSize);

        // 追加数据
        for (var i = 0; i < historicalData.Count; i++)
        {
            var dataRow = sheet.CreateRow(startRowNum + i);
            var rowData = historicalData[i];

            for (var j = 0; j < param.HistoricalDataParams.SignalVars.Count && j < rowData.Length; j++)
            {
                var signalVar = param.HistoricalDataParams.SignalVars[j];
                if (rowData[j] is double doubleValue)
                    dataRow.CreateCell(j).SetCellValue(ReportUnitParams.Conversion(signalVar.Unit, doubleValue));
                else if (rowData[j] is int intValue)
                    dataRow.CreateCell(j).SetCellValue(ReportUnitParams.Conversion(signalVar.Unit, intValue));
                else if (rowData[j] is string stringValue)
                    dataRow.CreateCell(j).SetCellValue(stringValue);
            }
        }

        // 释放内存
        historicalData.Clear();
    }

    /// <summary>
    /// 完成Excel导出
    /// </summary>
    /// <param name="param"></param>
    public static void FinalizeExcelExport(FinalizeExcelExportParams param)
    {
        try
        {
            lock (_fileLock)
            {
                try
                {
                    string actualFilePath = param.ExportReportFilePath;
                    (FileStream fs, IWorkbook workbook, List<SampleHistoricalDataInfo>? sampleHistoricalDataInfos, int historicalDataPageSize) fileInfo;

                    // 关闭并释放文件流和工作簿
                    if (!_openFiles.TryGetValue(param.ExportReportFilePath, out fileInfo))
                    {
                        // 尝试查找带时间戳的文件
                        var matchingFile = _openFiles.Keys.FirstOrDefault(key =>
                            key.Contains(Path.GetFileNameWithoutExtension(param.ExportReportFilePath)));

                        if (matchingFile != null)
                        {
                            actualFilePath = matchingFile;
                            fileInfo = _openFiles[matchingFile];
                        }
                        else
                        {
                            Console.WriteLine($"警告: 文件 {param.ExportReportFilePath} 未找到，可能已经被关闭。当前打开的文件: {string.Join(", ", _openFiles.Keys)}");
                            return;
                        }
                    }

                    var (fs, workbook, sampleHistoricalDataInfos, _) = fileInfo;

                    try
                    {
                        // 检查资源是否有效
                        if (fs != null && fs.CanWrite && workbook != null)
                        {
                            // 在保存前为历史数据sheet添加图表
                            if (sampleHistoricalDataInfos != null && sampleHistoricalDataInfos.Count > 0)
                            {
                                foreach (var sampleInfo in sampleHistoricalDataInfos)
                                {
                                    try
                                    {
                                        var sheet = workbook.GetSheet(sampleInfo.SampleName);
                                        if (sheet != null)
                                        {
                                            AddChartToHistoricalDataSheet(sheet);
                                        }
                                    }
                                    catch (Exception chartEx)
                                    {
                                        Logger.Error($"为历史数据sheet添加图表时出错: {chartEx.Message}. 试样: {sampleInfo.SampleName}. [CODE_VERSION:2024.12.08_V8_FIXED]");
                                    }
                                }
                            }

                            // 最后一次保存文件
                            // fs.Seek(0, SeekOrigin.Begin); // SXSSFWorkbook handles its own stream positioning
                            workbook.Write(fs, false); // Write and close the stream implicitly by SXSSFWorkbook
                            // fs.Flush(); // Not needed as Write(fs, false) should handle flushing.
                        }
                        else
                        {
                            Console.WriteLine($"警告: 文件流或工作簿已无效，跳过最终保存。文件路径: {actualFilePath}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"最终保存Excel文件时出错: {ex.Message}. 文件路径: {actualFilePath}. [CODE_VERSION:2024.12.08_V8_FIXED]\nStackTrace: {ex.StackTrace}");
                    }
                    finally
                    {
                        // 释放资源
                        try
                        {
                            workbook?.Close();
                            if (workbook is SXSSFWorkbook sxssfWorkbook)
                            {
                                sxssfWorkbook.Dispose(); // 确保SXSSFWorkbook的临时文件被清理
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"关闭工作簿时出错: {ex.Message}. 文件路径: {actualFilePath}. [CODE_VERSION:2024.12.08_V8_FIXED]\nStackTrace: {ex.StackTrace}");
                        }

                        try
                        {
                            fs?.Close();
                            fs?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"关闭文件流时出错: {ex.Message}. 文件路径: {actualFilePath}. [CODE_VERSION:2024.12.08_V8_FIXED]\nStackTrace: {ex.StackTrace}");
                        }

                        // 从字典中移除
                        _openFiles.Remove(actualFilePath);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"完成Excel导出时出错: {ex.Message}. 文件路径: {param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V8_FIXED]\nStackTrace: {ex.StackTrace}");
                    throw new Exception($"完成Excel导出时出错: {ex.Message}. 文件路径: {param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V8_FIXED]", ex);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.Error($"FinalizeExcelExport方法执行失败: {ex.Message}. 参数: ExportReportFilePath={param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V8_FIXED]\nStackTrace: {ex.StackTrace}");
            throw new Exception($"FinalizeExcelExport方法执行失败: {ex.Message}. 参数: ExportReportFilePath={param.ExportReportFilePath}. [CODE_VERSION:2024.12.08_V8_FIXED]", ex);
        }

        // 执行最终的清理和优化操作
        GC.Collect();
        GC.WaitForPendingFinalizers();
    }

    /// <summary>
    /// 创建报表的Sheet0(试样实例*结果变量信息)
    /// </summary>
    private static void CreateSheet0(this IWorkbook workbook, List<ReportSampleInstParams> sampleInsts)
    {
        var sheet = workbook.CreateSheet("基本信息");
        var rowNum = 0;
        // 遍历试样信息
        foreach (var sampleInst in sampleInsts)
        {
            // 试样名行
            var sampleInstRow = sheet.CreateRow(rowNum++);
            sampleInstRow.CreateCell(0).SetCellValue(sampleInst.Name);
            // header行
            var headerRow = sheet.CreateRow(rowNum++);
            headerRow.CreateCell(0).SetCellValue("Name");
            headerRow.CreateCell(1).SetCellValue("Value");
            // 试样实例输入变量
            foreach (var inputVar in sampleInst.InputVars)
            {
                var row = sheet.CreateRow(rowNum++);
                // 输入变量名
                row.CreateCell(0).SetCellValue(inputVar.Name);
                // 输入变量Value
                switch (inputVar.Value)
                {
                    case int intValue:
                        row.CreateCell(1).SetCellValue(
                            ReportUnitParams.Conversion(inputVar.Unit, intValue));
                        break;
                    case double doubleValue:
                        row.CreateCell(1).SetCellValue(
                            ReportUnitParams.Conversion(inputVar.Unit, doubleValue));
                        break;
                    case string stringValue:
                        row.CreateCell(1).SetCellValue(stringValue);
                        break;
                    default:
                        row.CreateCell(1).SetCellValue(inputVar.Value?.ToString());
                        break;
                }
                // 输入变量单位
                if (inputVar.Unit != null)
                    row.CreateCell(2).SetCellValue(inputVar.Unit.Code);
            }
        }
    }

    /// <summary>
    /// 创建报表的Sheet1 测试结果 = 试样实例*结果变量
    /// </summary>
    private static void CreateSheet1(this IWorkbook workbook,
        Dictionary<string, Dictionary<string, DbResultVar>> dbResultVars,
        List<ReportResultVarParams> resultVars, List<ReportSampleInstParams> sampleInsts)
    {
        // sheet1 测试结果
        var sheet = workbook.CreateSheet("测试结果");
        var rowNum = 0;
        // 测试结果表头 结果变量code 结果变量单位code
        var resultCodeRow = sheet.CreateRow(rowNum++);
        var resultUnitRow = sheet.CreateRow(rowNum++);
        for (var cellNum = 1; cellNum < resultVars.Count + 1; cellNum++)
        {
            var resultVar = resultVars[cellNum - 1];
            resultCodeRow.CreateCell(cellNum).SetCellValue(resultVar.Code);
            resultUnitRow.CreateCell(cellNum).SetCellValue(resultVar.Unit?.Code);
        }
        foreach (var sampleInst in sampleInsts)
        {
            // 每列的数据为试样的结果变量值
            var cellNum = 0;
            var instResultVarsRow = sheet.CreateRow(rowNum++);
            instResultVarsRow.CreateCell(cellNum++).SetCellValue(sampleInst.Name);
            dbResultVars.TryGetValue(sampleInst.Code, out var instResultVars);
            if (instResultVars == null) continue;
            foreach (var resultVar in resultVars)
            {
                // 结果变量值
                instResultVars.TryGetValue(resultVar.Code, out var dbResultVar);
                var resultVarValue = DbResultVar.GetValue(dbResultVar);
                if (resultVarValue == null)
                    cellNum++;
                else
                    instResultVarsRow.CreateCell(cellNum++).SetCellValue(
                        resultVar.FormatInfo.Format(
                            ReportUnitParams.Conversion(resultVar.Unit, (double)resultVarValue)));
            }
        }
    }

    /// <summary>
    ///  创建报表的Sheet2 数据统计 = Sheet2: 结果变量*统计函数
    /// </summary>
    private static void CreateSheet2(this IWorkbook workbook,
        Dictionary<string, Dictionary<string, DbResultVar>> dbResultVars,
        List<ReportResultVarParams> resultVars, List<ReportSampleInstParams> sampleInsts, List<StatisticalPattern> statisticalPatterns)
    {
        // sheet2 数据统计
        var sheet = workbook.CreateSheet("数据统计");
        var rowNum = 0;
        // 数据统计表头 结果变量code 结果变量单位code
        var resultCodeRow = sheet.CreateRow(rowNum++);
        var resultUnitRow = sheet.CreateRow(rowNum++);
        resultCodeRow.CreateCell(0).SetCellValue("结果文件");
        resultUnitRow.CreateCell(0).SetCellValue($"n = {sampleInsts.Count}");
        for (var cellNum = 1; cellNum < resultVars.Count + 1; cellNum++)
        {
            var resultVar = resultVars[cellNum - 1];
            resultCodeRow.CreateCell(cellNum).SetCellValue(resultVar.Code);
            resultUnitRow.CreateCell(cellNum).SetCellValue(resultVar.Unit?.Code);
        }

        foreach (var statisticalPattern in statisticalPatterns)
        {
            // sheet中的每行为一种统计方式
            var cellNum = 0;
            var statisticalRow = sheet.CreateRow(rowNum++);
            statisticalRow.CreateCell(cellNum++).SetCellValue(Statistic.GetStatisticalName(statisticalPattern));
            foreach (var resultVar in resultVars)
            {
                // 每列为一个统计结果
                // 结果变量值
                var resultVarValues = dbResultVars.Values
                    .Select(instResultVars =>
                    {
                        instResultVars.TryGetValue(resultVar.Code, out var dbResultVar);
                        return DbResultVar.GetValue(dbResultVar);
                    })
                    .Where(n => n.HasValue)
                    .Select(n => n!.Value)
                    .ToArray();
                // 空的数据不具有统计意义
                if (resultVarValues.Length == 0)
                    cellNum++;
                else
                    statisticalRow.CreateCell(cellNum++).SetCellValue(
                        resultVar.FormatInfo.Format(
                        ReportUnitParams.Conversion(resultVar.Unit,
                            Statistic.GetStatisticalResult(statisticalPattern, resultVarValues))));
            }
        }
    }

    /// <summary>
    /// 使用列的序号转为列名 
    /// </summary>
    private static string IndexToColumn(int index)
    {
        var result = string.Empty;
        while (index > 0)
        {
            result = (char)('A' + index % 26) + result;
            index /= 26;
        }
        return result;
    }

    /// <summary>
    /// 创建历史数据sheet页表头
    /// </summary>
    private static void CreateHistoricalDataSheetHeader(this IWorkbook workbook,
        ReportHistoricalDataParams historicalDataParams, string sampleInstName)
    {
        var sheet = workbook.CreateSheet(sampleInstName);
        // 表头 row1=试样名称 row2=信号变量名 row3=信号变量单位code
        var rowNum = 0;
        sheet.CreateRow(rowNum++).CreateCell(0).SetCellValue(sampleInstName);
        var signalNameRow = sheet.CreateRow(rowNum++);
        var signalUnitRow = sheet.CreateRow(rowNum++);
        List<ReportSignalVarParams> signalVars;
        if (string.IsNullOrEmpty(historicalDataParams.XAxisCode) ||
          string.IsNullOrEmpty(historicalDataParams.YAxisCode))
        {
            signalVars = historicalDataParams.SignalVars;
        }
        // 有xy轴code时 需要生成折线图 x轴数据 => 列0, y轴数据 => 列1
        else
        {
            // 先将x轴和y轴的信号变量放在前两列
            signalVars = new List<ReportSignalVarParams>();
            foreach (var signalVar in historicalDataParams.SignalVars)
            {
                // x轴 => 列0
                if (signalVar.Code == historicalDataParams.XAxisCode)
                    signalVars.Insert(0, signalVar);
                // y轴 => 列1(当判断的x轴未插入时y轴放在列0)
                else if (signalVar.Code == historicalDataParams.YAxisCode)
                    signalVars.Insert(signalVars.Count > 0 && signalVars[0].Code == historicalDataParams.XAxisCode ? 1 : 0, signalVar);
                // 默认信号变量放在列表最后                
                else
                    signalVars.Add(signalVar);
            }
        }

        for (var index = 0; index < signalVars.Count; index++)
        {
            var signalVar = signalVars[index];
            signalNameRow.CreateCell(index).SetCellValue(signalVar.Name);
            signalUnitRow.CreateCell(index).SetCellValue(signalVar.Unit?.Code);
        }
    }

    /// <summary>
    /// 为历史数据sheet添加图表
    /// </summary>
    private static void AddChartToHistoricalDataSheet(ISheet sheet)
    {
        try
        {
            // 获取sheet中的数据行数（跳过前3行表头）
            var lastRowNum = sheet.LastRowNum;
            if (lastRowNum <= 3) return; // 没有数据时不生成图表

            // 检查是否已有图表（避免重复添加）
            var drawing = sheet.DrawingPatriarch;
            // NPOI中IDrawing没有Charts属性，暂时跳过重复检查

            // 获取第一列和第二列的数据范围（假设前两列是要绘制的数据）
            var dataStartRow = 3; // 跳过表头的3行
            var dataEndRow = lastRowNum;

            // 创建图表区域（放在数据右侧）
            if (drawing == null)
                drawing = sheet.CreateDrawingPatriarch();

            var anchor = drawing.CreateAnchor(0, 0, 0, 0, 10, 5, 20, 25); // 图表位置和大小
            var chart = drawing.CreateChart(anchor);

            // 创建坐标轴
            var bottomAxis = chart.ChartAxisFactory.CreateValueAxis(AxisPosition.Bottom);
            var leftAxis = chart.ChartAxisFactory.CreateValueAxis(AxisPosition.Left);

            // 创建散点图数据
            var scatterChartData = chart.ChartDataFactory.CreateScatterChartData<double, double>();

            // 定义数据范围（第一列作为X轴，第二列作为Y轴）
            var xRange = new CellRangeAddress(dataStartRow, dataEndRow, 0, 0);
            var yRange = new CellRangeAddress(dataStartRow, dataEndRow, 1, 1);

            var xs = DataSources.FromNumericCellRange(sheet, xRange);
            var ys = DataSources.FromNumericCellRange(sheet, yRange);

            // 添加数据系列
            scatterChartData.AddSeries(xs, ys);

            // 绘制图表
            chart.Plot(scatterChartData, bottomAxis, leftAxis);
        }
        catch (Exception ex)
        {
            Logger.Error($"添加历史数据图表时出错: {ex.Message}. Sheet: {sheet.SheetName}. [CODE_VERSION:2024.12.08_V8_FIXED]");
        }
    }

    /// <summary>
    /// 创建历史数据sheet页
    /// </summary>
    private static void CreateHistoricalDataSheet(this IWorkbook workbook,
        List<ReportSignalVarParams> signalVars, string sampleInstName,
        IEnumerable<object[]> historicalData, bool generateChart)
    {
        // 受到excel行数上限104,8576的限制, 超过100,0000的数据在其他行使用
        const int upperLimitValue = 1000000;
        var rowDataList = historicalData.ToList();
        var historicalCount = rowDataList.Count;
        var cycles = (int)Math.Ceiling((double)historicalCount / upperLimitValue);

        var sheet = workbook.CreateSheet(sampleInstName);
        // 表头 row1=信号变量名 row2=信号变量单位code
        var rowNum = 0;
        sheet.CreateRow(rowNum++).CreateCell(0).SetCellValue(sampleInstName);
        var signalNameRow = sheet.CreateRow(rowNum++);
        // row3=单位code
        var signalUnitRow = sheet.CreateRow(rowNum++);

        for (var index = 0; index < signalVars.Count; index++)
        {
            var signalVar = signalVars[index];
            for (var cycle = 1; cycle <= cycles; cycle++)
            {
                var cellNum = index + ((cycle - 1) * (signalVars.Count + 1));
                signalNameRow.CreateCell(cellNum).SetCellValue(signalVar.Name);
                signalUnitRow.CreateCell(cellNum).SetCellValue(signalVar.Unit?.Code);
            }
        }
        // 数据 TODO: 获取历史数据方式待改善
        for (var rowIndex = 0; rowIndex < Math.Min(upperLimitValue, historicalCount); rowIndex++)
        {
            var dataRow = sheet.CreateRow(rowNum++);

            for (var cycle = 1; cycle <= cycles; cycle++)
            {
                var dataIndex = rowIndex + ((cycle - 1) * upperLimitValue);
                var rowData = historicalCount > dataIndex ? rowDataList[dataIndex] : null;
                if (rowData == null) continue;
                for (var index = 0; index < signalVars.Count; index++)
                {
                    var cellNum = index + ((cycle - 1) * (signalVars.Count + 1));
                    if (rowData[index] is double doubleValue)
                        dataRow.CreateCell(cellNum).SetCellValue(ReportUnitParams.Conversion(signalVars[index].Unit, doubleValue));
                    else if (rowData[index] is int intValue)
                        dataRow.CreateCell(cellNum).SetCellValue(ReportUnitParams.Conversion(signalVars[index].Unit, intValue));
                    else if (rowData[index] is string stringValue)
                        dataRow.CreateCell(cellNum).SetCellValue(stringValue);
                }
            }

        }
        var lastRowNum = rowNum;
        // 折线图 没有数据时row-num=3 此时不生成折线图
        if (!generateChart || lastRowNum <= 3) return;
        // 获取绘图实用的列
        var lastUsedCellNum = cycles * (signalVars.Count + 1);

        // 创建图标区域
        var drawing = sheet.CreateDrawingPatriarch();
        var anchor = drawing.CreateAnchor(0, 0, 0, 0, lastUsedCellNum + 5, 10, lastUsedCellNum + 15, 20);
        var chart = drawing.CreateChart(anchor);
        // xy轴
        var bottomAxis = chart.ChartAxisFactory.CreateValueAxis(AxisPosition.Bottom);
        var leftAxis = chart.ChartAxisFactory.CreateValueAxis(AxisPosition.Left);
        // 创建折线图
        var lineChartData = chart.ChartDataFactory.CreateScatterChartData<double, double>();

        // var xs = DataSources.FromNumericCellRange(sheet, CellRangeAddress.ValueOf(xCellRangeAddress));
        // var ys = DataSources.FromNumericCellRange(sheet, CellRangeAddress.ValueOf(yCellRangeAddress));
        //只可以用double类型绘制
        var xs = (IChartDataSource<double>)DataSources.FromArray(rowDataList.Select(arr => arr[0]).ToArray());
        var ys = (IChartDataSource<double>)DataSources.FromArray(rowDataList.Select(arr => arr[1]).ToArray());
        // 添加数据系列
        lineChartData.AddSeries(xs, ys);
        // 绘制折线图
        chart.Plot(lineChartData, bottomAxis, leftAxis);
    }

    /// <summary>
    /// 创建报表的其他Sheet(试样实例*信号变量历史数据)
    /// </summary>
    private static void CreateOtherSheets(this IWorkbook workbook,
        ITemplate template,
        List<ReportSampleInstParams> sampleInsts, ReportHistoricalDataParams historicalDataParams)
    {
        // 模板中调用时不生成历史数据sheet页 
        // 不包含bufferCode时不生成历史数据sheet页
        if (!template.IsProject || historicalDataParams.BufferCode == null) return;
        // 整理历史数据参数:
        bool generateChart;
        List<ReportSignalVarParams> signalVars;
        // xy轴任何一个code为空时不生成折线图(默认信号变量中一定包含xy轴code的信号变量)
        if (string.IsNullOrEmpty(historicalDataParams.XAxisCode) ||
            string.IsNullOrEmpty(historicalDataParams.YAxisCode))
        {
            generateChart = false;
            signalVars = historicalDataParams.SignalVars;
        }
        // 有xy轴code时 需要生成折线图 x轴数据 => 列0, y轴数据 => 列1
        else
        {
            generateChart = true;
            signalVars = new List<ReportSignalVarParams>();
            foreach (var signalVar in historicalDataParams.SignalVars)
            {
                // x轴 => 列0
                if (signalVar.Code == historicalDataParams.XAxisCode)
                    signalVars.Insert(0, signalVar);
                // y轴 => 列1(当判断的x轴未插入时y轴放在列0)
                else if (signalVar.Code == historicalDataParams.YAxisCode)
                    signalVars.Insert(signalVars.Count > 0 && signalVars[0].Code == historicalDataParams.XAxisCode ? 1 : 0, signalVar);
                // 默认信号变量放在列表最后                
                else
                    signalVars.Add(signalVar);
            }
        }
        foreach (var sampleInst in sampleInsts)
        {
            var sqlTableName = sampleInst.Code + historicalDataParams.BufferCode;
            var historicalData = template.Db.GetHistoricalData(
                tableName: sqlTableName,
                signalCodes: signalVars.Select(signalVar => signalVar.Code).ToArray());
            workbook.CreateHistoricalDataSheet(signalVars, sampleInst.Name, historicalData, generateChart);
        }
    }

    /// <summary>
    /// 创建测试结果sheet的表头
    /// </summary>
    private static void CreateSheet1Header(this IWorkbook workbook, List<ReportResultVarParams> resultVars)
    {
        var sheet = workbook.CreateSheet("测试结果");
        var rowNum = 0;
        // 测试结果表头 结果变量code 结果变量单位code
        var resultCodeRow = sheet.CreateRow(rowNum++);
        var resultUnitRow = sheet.CreateRow(rowNum++);
        for (var cellNum = 1; cellNum < resultVars.Count + 1; cellNum++)
        {
            var resultVar = resultVars[cellNum - 1];
            resultCodeRow.CreateCell(cellNum).SetCellValue(resultVar.Code);
            resultUnitRow.CreateCell(cellNum).SetCellValue(resultVar.Unit?.Code);
        }
    }

    /// <summary>
    /// 创建数据统计sheet的表头
    /// </summary>
    private static void CreateSheet2Header(this IWorkbook workbook, List<ReportResultVarParams> resultVars, List<StatisticalPattern> statisticalPatterns)
    {
        var sheet = workbook.CreateSheet("数据统计");
        var rowNum = 0;
        // 数据统计表头 结果变量code 结果变量单位code
        var resultCodeRow = sheet.CreateRow(rowNum++);
        var resultUnitRow = sheet.CreateRow(rowNum++);
        resultCodeRow.CreateCell(0).SetCellValue("结果文件");
        for (var cellNum = 1; cellNum < resultVars.Count + 1; cellNum++)
        {
            var resultVar = resultVars[cellNum - 1];
            resultCodeRow.CreateCell(cellNum).SetCellValue(resultVar.Code);
            resultUnitRow.CreateCell(cellNum).SetCellValue(resultVar.Unit?.Code);
        }
    }

    /// <summary>
    /// 追加数据到各个sheet
    /// </summary>
    private static void AppendDataToSheets(this IWorkbook workbook, List<string> sampleCodes, List<ReportSampleInstParams> sampleInsts, int pageIndex,
        List<ReportResultVarParams> resultVars, List<StatisticalPattern> statisticalPatterns, string templateName)
    {
        var template = ITemplate.GetTemplateByName(templateName);
        if (template is not { IsProject: true }) return;

        // 获取当前页样本的结果变量数据
        var dbResultVars = DbResultVar.GetDbResultVars(template,
            sampleCodes.ToArray(),
            resultVars.Select(resultVar => resultVar.Code).ToArray());

        // 获取测试结果sheet并追加数据
        var testResultSheet = workbook.GetSheet("测试结果");
        if (testResultSheet != null)
        {
            AppendTestResultData(testResultSheet, dbResultVars, resultVars, sampleCodes, sampleInsts, pageIndex);
        }

        // 获取数据统计sheet并追加数据
        var statisticsSheet = workbook.GetSheet("数据统计");
        if (statisticsSheet != null)
        {
            AppendStatisticsData(statisticsSheet, dbResultVars, resultVars, statisticalPatterns, pageIndex);
        }
    }

    /// <summary>
    /// 追加测试结果数据
    /// </summary>
    private static void AppendTestResultData(ISheet sheet, Dictionary<string, Dictionary<string, DbResultVar>> dbResultVars,
        List<ReportResultVarParams> resultVars, List<string> sampleCodes, List<ReportSampleInstParams> sampleInsts, int pageIndex)
    {
        var startRowIndex = 2 + (pageIndex * sampleCodes.Count); // 跳过表头行
        var rowNum = startRowIndex;

        foreach (var sampleCode in sampleCodes)
        {
            var cellNum = 0;
            var instResultVarsRow = sheet.CreateRow(rowNum++);

            // 查找对应的试样实例，使用试样名称而不是试样代码
            var sampleInst = sampleInsts.FirstOrDefault(s => s.Code == sampleCode);
            var sampleName = sampleInst?.Name ?? sampleCode; // 如果找不到试样实例，则使用试样代码作为备选

            instResultVarsRow.CreateCell(cellNum++).SetCellValue(sampleName);
            dbResultVars.TryGetValue(sampleCode, out var instResultVars);
            if (instResultVars == null) continue;

            foreach (var resultVar in resultVars)
            {
                instResultVars.TryGetValue(resultVar.Code, out var dbResultVar);
                var resultVarValue = DbResultVar.GetValue(dbResultVar);
                if (resultVarValue == null)
                    cellNum++;
                else
                    instResultVarsRow.CreateCell(cellNum++).SetCellValue(
                        resultVar.FormatInfo.Format(
                            ReportUnitParams.Conversion(resultVar.Unit, (double)resultVarValue)));
            }
        }
    }

    /// <summary>
    /// 追加统计数据
    /// </summary>
    private static void AppendStatisticsData(ISheet sheet, Dictionary<string, Dictionary<string, DbResultVar>> dbResultVars,
        List<ReportResultVarParams> resultVars, List<StatisticalPattern> statisticalPatterns, int pageIndex)
    {
        // 只在第一页时计算和添加统计数据
        if (pageIndex > 0) return;

        var rowNum = 2; // 跳过表头行

        foreach (var statisticalPattern in statisticalPatterns)
        {
            var cellNum = 0;
            var statisticalRow = sheet.CreateRow(rowNum++);
            statisticalRow.CreateCell(cellNum++).SetCellValue(Statistic.GetStatisticalName(statisticalPattern));

            foreach (var resultVar in resultVars)
            {
                var resultVarValues = dbResultVars.Values
                    .Select(instResultVars =>
                    {
                        instResultVars.TryGetValue(resultVar.Code, out var dbResultVar);
                        return DbResultVar.GetValue(dbResultVar);
                    })
                    .Where(n => n.HasValue)
                    .Select(n => n!.Value)
                    .ToArray();

                if (resultVarValues.Length == 0)
                    cellNum++;
                else
                    statisticalRow.CreateCell(cellNum++).SetCellValue(
                        resultVar.FormatInfo.Format(
                            ReportUnitParams.Conversion(resultVar.Unit,
                                Statistic.GetStatisticalResult(statisticalPattern, resultVarValues))));
            }
        }
    }
    /// <summary>
    /// 导出DoubleArray
    /// </summary>
    /// <param name="param"></param>
    internal static void ExportCsvDoubleArray(ExportDoubleArrayParams param)
    {
        var _template = GetTemplateByName(param.TemplateName!);
        if (param.Type == "DoubleArray")
        {
            var _InputVar = (DoubleArrayInputVar)_template.CurrentInst.InputVars[param.ArrayCode];
            _InputVar.CsvExport(param.Codes, param.Path, param.FileName);
        }
        else if (param.Type == "DoubleArrayList")
        {
            var _InputVar = (DoubleArrayListInputVar)_template.CurrentInst.InputVars[param.ArrayCode];
            _InputVar.CsvExport(param.Codes, param.Path, param.FileName);
        }

    }
    /// <summary>
    /// 导出DoubleArray(CSV格式)
    /// </summary>
    /// <param name="param"></param>
    internal static void ExportDoubleArray(ExportDoubleArrayParams param)
    {
        var _template = GetTemplateByName(param.TemplateName!);
        if (param.Type == "DoubleArray")
        {
            var _InputVar = (DoubleArrayInputVar)_template.CurrentInst.InputVars[param.ArrayCode];
            _InputVar.ReportExport(param.Codes, param.Path, param.FileName);
        }
        else if (param.Type == "DoubleArrayList")
        {
            var _InputVar = (DoubleArrayListInputVar)_template.CurrentInst.InputVars[param.ArrayCode];
            _InputVar.ReportExport(param.Codes, param.Path, param.FileName);
        }
    }
    

}