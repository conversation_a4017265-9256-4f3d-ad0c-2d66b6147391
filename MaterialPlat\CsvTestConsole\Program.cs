using System.Text;

namespace CsvAnalyzer;

/// <summary>
/// CSV数据分析器主程序
/// </summary>
class Program
{
    static void Main(string[] args)
    {
        // 注册编码提供程序以支持GB2312等编码
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        
        string csvFilePath = Path.Combine("TestData", "sample_data.csv");
        
        try
        {
            var analyzer = new CsvDataAnalyzer();
            var result = analyzer.AnalyzeCsvFile(csvFilePath);
            
            // 输出分析结果
            Console.WriteLine("=== CSV文件分析结果 ===");
            Console.WriteLine($"文件路径: {csvFilePath}");
            Console.WriteLine($"总行数: {result.TotalRows}");
            Console.WriteLine($"有效数据行数: {result.ValidRows}");
            Console.WriteLine();
            
            // 第一列分析结果
            Console.WriteLine("=== 第一列相邻数值差值分析 ===");
            if (result.FirstColumnDifferencesAllOne)
            {
                Console.WriteLine("✓ 第一列相邻数值的差值均为1");
            }
            else
            {
                Console.WriteLine("✗ 第一列相邻数值的差值不全为1，发现以下异常:");
                foreach (var anomaly in result.FirstColumnAnomalies)
                {
                    Console.WriteLine($"  行 {anomaly.RowIndex}: {anomaly.PreviousValue} -> {anomaly.CurrentValue} (差值: {anomaly.Difference})");
                }
            }
            Console.WriteLine();
            
            // 第二列分析结果
            Console.WriteLine("=== 第二列相邻数值差值范围分析 ===");
            Console.WriteLine("注意：差值计算为原始差值（非绝对值），可能为正数或负数");
            Console.WriteLine();
            if (result.ValidRows > 1)
            {
                Console.WriteLine($"最小差值: {result.SecondColumnMinDifference:F6}");
                if (result.MinDifferenceDetail != null)
                {
                    Console.WriteLine($"  对应数据: 第{result.MinDifferenceDetail.RowIndex}行 {result.MinDifferenceDetail.PreviousValue:F6} -> {result.MinDifferenceDetail.CurrentValue:F6}");
                }
                Console.WriteLine();
                
                Console.WriteLine($"最大差值: {result.SecondColumnMaxDifference:F6}");
                if (result.MaxDifferenceDetail != null)
                {
                    Console.WriteLine($"  对应数据: 第{result.MaxDifferenceDetail.RowIndex}行 {result.MaxDifferenceDetail.PreviousValue:F6} -> {result.MaxDifferenceDetail.CurrentValue:F6}");
                }
                Console.WriteLine();
                
                Console.WriteLine($"平均差值: {result.SecondColumnAverageDifference:F6}");
                Console.WriteLine($"差值范围: [{result.SecondColumnMinDifference:F6}, {result.SecondColumnMaxDifference:F6}]");
                Console.WriteLine();
                
                // 差值区间统计
                Console.WriteLine("=== 差值绝对值区间统计 ===");
                Console.WriteLine($"差值绝对值 ≤ 0.01: {result.DifferenceWithin001Count} 行");
                Console.WriteLine($"差值绝对值 0.01 ~ 0.02: {result.DifferenceBetween001And002Count} 行");
                Console.WriteLine($"差值绝对值 0.02 ~ 0.03: {result.DifferenceBetween002And003Count} 行");
                Console.WriteLine($"差值绝对值 > 0.03: {result.DifferenceAbove003Count} 行");
                
                int totalDifferenceRows = result.DifferenceWithin001Count + result.DifferenceBetween001And002Count + 
                                         result.DifferenceBetween002And003Count + result.DifferenceAbove003Count;
                Console.WriteLine($"总差值记录行数: {totalDifferenceRows} 行");
            }
            else
            {
                Console.WriteLine("数据行数不足，无法计算相邻差值");
            }
        }
        catch (FileNotFoundException ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
        }
        catch (InvalidDataException ex)
        {
            Console.WriteLine($"数据错误: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"未知错误: {ex.Message}");
        }
        
        Console.WriteLine();
        Console.WriteLine("=== 分析完成 ===");
        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}