using NUnit.Framework;
using System.Text;

namespace CsvAnalyzer.Tests;

/// <summary>
/// CSV数据分析器单元测试
/// </summary>
[TestFixture]
public class CsvAnalyzerTests
{
    private CsvDataAnalyzer _analyzer;
    private string _testDataDirectory;

    [SetUp]
    public void Setup()
    {
        _analyzer = new CsvDataAnalyzer();
        _testDataDirectory = Path.Combine(Path.GetTempPath(), "CsvAnalyzerTests");
        Directory.CreateDirectory(_testDataDirectory);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_testDataDirectory))
        {
            Directory.Delete(_testDataDirectory, true);
        }
    }

    /// <summary>
    /// 测试正常的CSV文件分析 - 第一列差值均为1
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_NormalSequence_ShouldReturnCorrectResult()
    {
        // Arrange
        var testData = "序号,裂纹长度\n1,25.125\n2,25.136\n3,25.134\n4,25.139";
        var testFile = CreateTestCsvFile("normal_test.csv", testData);

        // Act
        var result = _analyzer.AnalyzeCsvFile(testFile);

        // Assert
        Assert.That(result.TotalRows, Is.EqualTo(5)); // 包含标题行
        Assert.That(result.ValidRows, Is.EqualTo(4)); // 4行有效数据
        Assert.That(result.FirstColumnDifferencesAllOne, Is.True);
        Assert.That(result.FirstColumnAnomalies, Is.Empty);
        Assert.That(result.SecondColumnMinDifference, Is.EqualTo(-0.002).Within(0.001));
        Assert.That(result.SecondColumnMaxDifference, Is.EqualTo(0.011).Within(0.001));
        
        // 验证最小差值和最大差值对应的原始数据
        Assert.That(result.MinDifferenceDetail, Is.Not.Null);
        Assert.That(result.MaxDifferenceDetail, Is.Not.Null);
        Assert.That(result.MinDifferenceDetail.Difference, Is.EqualTo(-0.002).Within(0.001));
        Assert.That(result.MaxDifferenceDetail.Difference, Is.EqualTo(0.011).Within(0.001));
        
        // 验证差值区间统计
        // 测试数据差值：0.011, -0.002, 0.005
        // 绝对值：0.011, 0.002, 0.005
        // 0.011 > 0.01，在0.01~0.02区间；0.002和0.005 ≤ 0.01
        Assert.That(result.DifferenceWithin001Count, Is.EqualTo(2));
        Assert.That(result.DifferenceBetween001And002Count, Is.EqualTo(1));
        Assert.That(result.DifferenceBetween002And003Count, Is.EqualTo(0));
        Assert.That(result.DifferenceAbove003Count, Is.EqualTo(0));
    }

    /// <summary>
    /// 测试第一列存在异常差值的情况
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_AbnormalSequence_ShouldDetectAnomalies()
    {
        // Arrange
        var testData = "序号,裂纹长度\n1,25.125\n3,25.136\n4,25.134\n6,25.139";
        var testFile = CreateTestCsvFile("abnormal_test.csv", testData);

        // Act
        var result = _analyzer.AnalyzeCsvFile(testFile);

        // Assert
        Assert.That(result.FirstColumnDifferencesAllOne, Is.False);
        Assert.That(result.FirstColumnAnomalies, Has.Count.EqualTo(2));
        
        // 检查第一个异常：1->3，差值为2
        var firstAnomaly = result.FirstColumnAnomalies[0];
        Assert.That(firstAnomaly.PreviousValue, Is.EqualTo(1));
        Assert.That(firstAnomaly.CurrentValue, Is.EqualTo(3));
        Assert.That(firstAnomaly.Difference, Is.EqualTo(2));
        
        // 检查第二个异常：4->6，差值为2
        var secondAnomaly = result.FirstColumnAnomalies[1];
        Assert.That(secondAnomaly.PreviousValue, Is.EqualTo(4));
        Assert.That(secondAnomaly.CurrentValue, Is.EqualTo(6));
        Assert.That(secondAnomaly.Difference, Is.EqualTo(2));
    }

    /// <summary>
    /// 测试第二列差值范围计算
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_SecondColumnDifferences_ShouldCalculateCorrectRange()
    {
        // Arrange
        var testData = "序号,裂纹长度\n1,25.100\n2,25.150\n3,25.120\n4,25.180\n5,25.110";
        var testFile = CreateTestCsvFile("range_test.csv", testData);

        // Act
        var result = _analyzer.AnalyzeCsvFile(testFile);

        // Assert
        // 差值序列：0.05, -0.03, 0.06, -0.07
        Assert.That(result.SecondColumnMinDifference, Is.EqualTo(-0.07).Within(0.001));
        Assert.That(result.SecondColumnMaxDifference, Is.EqualTo(0.06).Within(0.001));
        Assert.That(result.SecondColumnAverageDifference, Is.EqualTo(0.0025).Within(0.001));
    }

    /// <summary>
    /// 测试空文件处理
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_EmptyFile_ShouldThrowException()
    {
        // Arrange
        var testFile = CreateTestCsvFile("empty_test.csv", "");

        // Act & Assert
        Assert.Throws<InvalidDataException>(() => _analyzer.AnalyzeCsvFile(testFile));
    }

    /// <summary>
    /// 测试只有标题行的文件
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_HeaderOnly_ShouldThrowException()
    {
        // Arrange
        var testData = "序号,裂纹长度";
        var testFile = CreateTestCsvFile("header_only_test.csv", testData);

        // Act & Assert
        Assert.Throws<InvalidDataException>(() => _analyzer.AnalyzeCsvFile(testFile));
    }

    /// <summary>
    /// 测试文件不存在的情况
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_FileNotExists_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentFile = Path.Combine(_testDataDirectory, "non_existent.csv");

        // Act & Assert
        Assert.Throws<FileNotFoundException>(() => _analyzer.AnalyzeCsvFile(nonExistentFile));
    }

    /// <summary>
    /// 测试包含无效数据的CSV文件
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_InvalidData_ShouldSkipInvalidRows()
    {
        // Arrange
        var testData = "序号,裂纹长度\n1,25.125\ninvalid,data\n2,25.136\n3,25.134";
        var testFile = CreateTestCsvFile("invalid_data_test.csv", testData);

        // Act
        var result = _analyzer.AnalyzeCsvFile(testFile);

        // Assert
        Assert.That(result.TotalRows, Is.EqualTo(5)); // 包含标题行和无效行
        Assert.That(result.ValidRows, Is.EqualTo(3)); // 只有3行有效数据
        Assert.That(result.FirstColumnDifferencesAllOne, Is.True);
    }

    /// <summary>
    /// 测试单行数据的情况
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_SingleDataRow_ShouldReturnValidResult()
    {
        // Arrange
        var testData = "序号,裂纹长度\n1,25.125";
        var testFile = CreateTestCsvFile("single_row_test.csv", testData);

        // Act
        var result = _analyzer.AnalyzeCsvFile(testFile);

        // Assert
        Assert.That(result.TotalRows, Is.EqualTo(2));
        Assert.That(result.ValidRows, Is.EqualTo(1));
        Assert.That(result.FirstColumnDifferencesAllOne, Is.True); // 没有相邻数据可比较
        Assert.That(result.FirstColumnAnomalies, Is.Empty);
        Assert.That(result.SecondColumnMinDifference, Is.EqualTo(0));
        Assert.That(result.SecondColumnMaxDifference, Is.EqualTo(0));
    }

    /// <summary>
    /// 测试大数据量的性能
    /// </summary>
    [Test]
    public void AnalyzeCsvFile_LargeDataSet_ShouldCompleteInReasonableTime()
    {
        // Arrange
        var sb = new StringBuilder();
        sb.AppendLine("序号,裂纹长度");
        for (int i = 1; i <= 10000; i++)
        {
            sb.AppendLine($"{i},{25.0 + i * 0.001}");
        }
        var testFile = CreateTestCsvFile("large_data_test.csv", sb.ToString());

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = _analyzer.AnalyzeCsvFile(testFile);
        stopwatch.Stop();

        // Assert
        Assert.That(result.ValidRows, Is.EqualTo(10000));
        Assert.That(result.FirstColumnDifferencesAllOne, Is.True);
        Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(5000)); // 应在5秒内完成
    }

    /// <summary>
    /// 创建测试用的CSV文件
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <param name="content">文件内容</param>
    /// <returns>文件路径</returns>
    private string CreateTestCsvFile(string fileName, string content)
    {
        var filePath = Path.Combine(_testDataDirectory, fileName);
        File.WriteAllText(filePath, content, Encoding.UTF8);
        return filePath;
    }
}

/// <summary>
/// CSV分析结果测试
/// </summary>
[TestFixture]
public class CsvAnalysisResultTests
{
    /// <summary>
    /// 测试CsvAnalysisResult的默认值
    /// </summary>
    [Test]
    public void CsvAnalysisResult_DefaultValues_ShouldBeCorrect()
    {
        // Arrange & Act
        var result = new CsvAnalysisResult();

        // Assert
        Assert.That(result.TotalRows, Is.EqualTo(0));
        Assert.That(result.ValidRows, Is.EqualTo(0));
        Assert.That(result.FirstColumnDifferencesAllOne, Is.False);
        Assert.That(result.FirstColumnAnomalies, Is.Not.Null);
        Assert.That(result.FirstColumnAnomalies, Is.Empty);
        Assert.That(result.SecondColumnMinDifference, Is.EqualTo(0));
        Assert.That(result.SecondColumnMaxDifference, Is.EqualTo(0));
        Assert.That(result.SecondColumnAverageDifference, Is.EqualTo(0));
    }
}

/// <summary>
/// 列异常信息测试
/// </summary>
[TestFixture]
public class ColumnAnomalyTests
{
    /// <summary>
    /// 测试ColumnAnomaly属性设置
    /// </summary>
    [Test]
    public void ColumnAnomaly_Properties_ShouldSetCorrectly()
    {
        // Arrange & Act
        var anomaly = new ColumnAnomaly
        {
            RowIndex = 5,
            PreviousValue = 3,
            CurrentValue = 6,
            Difference = 3
        };

        // Assert
        Assert.That(anomaly.RowIndex, Is.EqualTo(5));
        Assert.That(anomaly.PreviousValue, Is.EqualTo(3));
        Assert.That(anomaly.CurrentValue, Is.EqualTo(6));
        Assert.That(anomaly.Difference, Is.EqualTo(3));
    }
}