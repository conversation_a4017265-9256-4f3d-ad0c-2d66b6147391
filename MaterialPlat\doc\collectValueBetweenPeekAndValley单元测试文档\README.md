# collectValueBetweenPeekAndValley 单元测试文档

## 概述

本文档描述了为 `collectValueBetweenPeekAndValley` 方法创建的单元测试，该测试实现了从CSV文件读取数据并将处理结果写入CSV文件的完整流程。

## 测试方法信息

### 方法名称
`CollectValueBetweenPeekAndValley_FromCsvFile_SampleType0`

### 测试目标
- 测试 `collectValueBetweenPeekAndValley` 方法在 `sampleType=0`（紧凑型试样）条件下的功能
- 验证从CSV文件读取数据的能力
- 验证将处理结果写入CSV文件的能力
- 确保数据处理的正确性和一致性

## 测试数据

### 输入数据特征
- **数据类型**: 模拟紧凑型试样的负荷-变形曲线
- **数据模式**: 先上升到峰值，再下降到谷值
- **数据长度**: 61个数据点
- **数据范围**: 
  - 负荷数组: -10 到 20
  - 变形数组: -10 到 20

### 具体测试数据
```csharp
// 负荷数组
{ 0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10,  // 初始下降段
  1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,  // 上升到峰值
  19, 18, 17, 16, 15, 14, 13, 12, 11, 10,  // 从峰值下降
  9, 8, 7, 6, 5, 4, 3, 2, 1, 0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10 }  // 继续下降到谷值

// 变形数组（与负荷数组相同）
{ 0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10,
  1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
  19, 18, 17, 16, 15, 14, 13, 12, 11, 10,
  9, 8, 7, 6, 5, 4, 3, 2, 1, 0, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10 }
```

## 测试流程

### 1. 准备阶段
- 创建测试数据目录 `TestData`
- 定义输入和输出CSV文件路径
- 生成包含测试数据的输入CSV文件

### 2. 执行阶段
- 从CSV文件读取负荷和变形数据
- 调用 `collectValueBetweenPeekAndValley` 方法处理数据
- 将处理结果写入输出CSV文件

### 3. 验证阶段
- 验证方法返回值（应为0，表示成功）
- 验证输出数组不为null且长度相等
- 验证输出数组包含有效数据
- 验证输出文件成功创建
- 验证输出文件内容与处理结果一致

### 4. 清理阶段
- 删除测试过程中创建的临时文件

## 测试结果

### 执行结果
- ✅ 测试通过
- ✅ 输入数据长度: 61个数据点
- ✅ 输出数据长度: 18个数据点
- ✅ 数据处理正确，峰谷值提取成功

### 文件生成
- **输入文件**: `input_data_sampletype0.csv`
- **输出文件**: `output_data_sampletype0.csv`
- **文件格式**: UTF-8编码的CSV文件

## CSV文件格式

### 输入CSV文件格式
```csv
负荷,变形
0,0
-1,-1
-2,-2
...
```

### 输出CSV文件格式
```csv
处理后负荷,处理后变形
20,20
19,19
18,18
...
```

## 技术实现细节

### 使用的库
- **CsvHelper**: 用于CSV文件的读写操作
- **NUnit**: 用于单元测试框架
- **System.IO**: 用于文件操作

### 关键方法

#### CreateInputCsvFile
- 创建包含测试数据的输入CSV文件
- 使用UTF-8编码
- 包含表头行

#### ReadDataFromCsv
- 从CSV文件读取数据到数组
- 跳过表头行
- 处理数据类型转换

#### WriteDataToCsv
- 将数组数据写入CSV文件
- 使用UTF-8编码
- 包含描述性表头

## 验证要点

1. **功能正确性**: 方法能正确识别峰值和谷值，提取中间数据
2. **数据完整性**: 输入输出数据长度和内容的一致性
3. **文件操作**: CSV文件的正确读写
4. **异常处理**: 测试过程中的资源清理
5. **编码兼容**: UTF-8编码的正确处理

## 扩展建议

1. **添加更多测试用例**:
   - 测试 `sampleType=1`（三点弯试样）
   - 测试边界条件（空数组、单点数据等）
   - 测试异常数据（包含NaN、无穷大等）

2. **增强文件处理**:
   - 测试不同编码格式的CSV文件
   - 测试大文件处理性能
   - 添加文件路径异常处理测试

3. **参数化测试**:
   - 使用不同的 `maxRate` 和 `minRate` 参数
   - 测试不同规模的数据集

## 运行测试

```bash
# 运行特定测试
dotnet test MaterialPlat/FuncLibsTests/FuncLibsTests.csproj --filter "CollectValueBetweenPeekAndValley_FromCsvFile_SampleType0" --verbosity normal

# 运行所有相关测试
dotnet test MaterialPlat/FuncLibsTests/FuncLibsTests.csproj --filter "collectValueBetweenPeekAndValley" --verbosity normal
```

## 总结

该单元测试成功验证了 `collectValueBetweenPeekAndValley` 方法在处理CSV文件数据时的正确性和可靠性。测试覆盖了完整的数据处理流程，从文件读取到数据处理再到结果输出，确保了方法在实际应用场景中的稳定性。
