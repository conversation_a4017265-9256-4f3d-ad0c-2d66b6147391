G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\5 窗体汇总\Window1.g.cs
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16_MarkupCompile.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16_MarkupCompile.lref
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\bin\Debug\HwSim16.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\bin\Debug\HwSim16.pdb
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\bin\Debug\C1.WPF.C1Chart.4.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\bin\Debug\IHardware.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\bin\Debug\IHardware.pdb
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\bin\Debug\C1.WPF.C1Chart.4.xml
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\5 窗体汇总\Window1.baml
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.g.resources
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.dll.licenses
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\HwSim16.pdb
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\5 窗体汇总\PID.g.cs
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.g.cs
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\5 窗体汇总\PID.baml
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.baml
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\IHardware.dll
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\IHardware.pdb
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\PID.g.cs
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.g.cs
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\Window1.g.cs
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16_MarkupCompile.cache
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16_MarkupCompile.lref
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\PID.baml
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.baml
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\Window1.baml
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.g.resources
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.dll.licenses
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.pdb
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\HwSim16.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\HwSim16.pdb
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\C1.WPF.C1Chart.4.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\IHardware.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\IHardware.pdb
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\bin\Debug\C1.WPF.C1Chart.4.xml
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\PID.g.cs
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.g.cs
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\Window1.g.cs
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16_MarkupCompile.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16_MarkupCompile.lref
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\PID.baml
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.baml
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\Window1.baml
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.g.resources
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.dll.licenses
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.dll
G:\居家办公\软件平台\硬件公共接口\16通道模拟程序\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\PID.g.cs
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.g.cs
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\Window1.g.cs
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16_MarkupCompile.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16_MarkupCompile.lref
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\bin\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\bin\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\bin\Debug\IHardware.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\bin\Debug\IHardware.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\PID.baml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\SensorCalibration.baml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\5 窗体汇总\Window1.baml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.g.resources
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.dll.licenses
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.dll.licenses
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\bin\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\bin\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\bin\Debug\IHardware.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\bin\Debug\IHardware.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\IHardware.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\IHardware.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.dll.licenses
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\obj\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\HwSim16.dll.config
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Buffers.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Memory.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Numerics.Vectors.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Text.Encodings.Web.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Threading.Tasks.Extensions.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.ValueTuple.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Buffers.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Memory.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Numerics.Vectors.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Text.Encodings.Web.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.Threading.Tasks.Extensions.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\System.ValueTuple.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\Newtonsoft.Json.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 无wpf 32位\HwSim\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Desktop\pid\net6.0\HwSim16.dll.config
C:\Users\<USER>\Desktop\pid\net6.0\HwSim16.dll
C:\Users\<USER>\Desktop\pid\net6.0\HwSim16.pdb
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll.config
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.pdb
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.pdb
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.xml
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll.licenses
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll
C:\Users\<USER>\Desktop\pid\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.pdb
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll.licenses
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.pdb
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll.config
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.pdb
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.dll
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.pdb
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.xml
C:\Users\<USER>\Desktop\pid1\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll.config
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.pdb
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.pdb
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.xml
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll.licenses
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll
D:\微信聊天文件\WeChat Files\coollittle\FileStorage\File\2023-11\HwSim16-framework4.8 - 添加HC 2023-11-14\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.pdb
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll.config
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.pdb
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.pdb
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.xml
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll.licenses
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll
D:\hc2022\zhongji\099中机提供软件和硬件接口文档\HwSim16-framework4.8 - 添加HC-李涛调整2023年11月14日\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.Properties.Resources.resources
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.GenerateResource.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll.licenses
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll.config
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\HwSim16.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\MessagePack.Annotations.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\IHardware.pdb
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\Newtonsoft.Json.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Buffers.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Memory.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Numerics.Vectors.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Text.Encodings.Web.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.Threading.Tasks.Extensions.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\System.ValueTuple.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\bin\Debug\MessagePack.Annotations.xml
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.csproj.CopyComplete
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.dll
E:\zy\软件平台\硬件公共接口\16通道硬件接口\HwSim16-framework4.8 - 添加HC\HwSim\obj\Debug\HwSim16.pdb
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\HwSim16.dll.config
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\HwSim16.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\HwSim16.pdb
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\IHardware.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\Newtonsoft.Json.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Buffers.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Memory.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Numerics.Vectors.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Text.Encodings.Web.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Threading.Tasks.Extensions.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.ValueTuple.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\MessagePack.Annotations.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\IHardware.pdb
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\Newtonsoft.Json.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Buffers.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Memory.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Numerics.Vectors.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Text.Encodings.Web.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.Threading.Tasks.Extensions.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\System.ValueTuple.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\bin\Debug\MessagePack.Annotations.xml
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\obj\Debug\HwSim16.csproj.AssemblyReference.cache
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\obj\Debug\HwSim16.Properties.Resources.resources
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\obj\Debug\HwSim16.csproj.GenerateResource.cache
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\obj\Debug\HwSim16.csproj.CoreCompileInputs.cache
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\obj\Debug\HwSim16.csproj.Up2Date
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\obj\Debug\HwSim16.dll
d:\WorkProject\ZJ\MaterialPlat\HardwareSim\Hwsim\obj\Debug\HwSim16.pdb
