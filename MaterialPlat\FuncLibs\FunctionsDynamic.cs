using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace FuncLibs
{
    /// <summary>
    /// 动态部门提供的计算函数
    /// </summary>
    public static class FunctionsDynamic
    {
        [DllImport("GBT26077.dll", EntryPoint = "LeastSquaresLinearFitting", CallingConvention = CallingConvention.Cdecl)]
        public static extern int LeastSquaresLinearFitting(double[] arrayX, double[] arrayY, int size, out double slope, out double intercept);

        [DllImport("GBT26077.dll", EntryPoint = "FindSegments", CallingConvention = CallingConvention.Cdecl)]
        public static extern double FindSegments(double[] arrayY, int size, double a, double b);


        [DllImport("MetallicMaterials-FatigueTesting-AnalysisOfData.dll", EntryPoint = "FatigueData_ErrorBound", CallingConvention = CallingConvention.Cdecl)]
        /// <summary>
        /// 计算子样平均值的误差限度
        /// </summary>
        /// <param name="data">数组</param>
        /// <param name="size">数组大小</param>
        /// <param name="confidenceLevel">置信度</param>
        /// <param name="errorBound">误差限度</param>
        /// <returns>0为正常；-1为异常</returns>
        public static extern int FatigueDataErrorBound(double[] data, int size, double confidenceLevel, out double errorBound);

        [DllImport("MetallicMaterials-FatigueTesting-AnalysisOfData.dll", EntryPoint = "FatigueData_LowerLimit", CallingConvention = CallingConvention.Cdecl)]
        /// <summary>
        /// 给定置信度和存活率计算下极限
        /// </summary>
        /// <param name="data">数组</param>
        /// <param name="size">数组大小</param>
        /// <param name="confidenceLevel">置信度</param>
        /// <param name="survivalRate">存活率</param>
        /// <param name="lowerLimit">下极限</param>
        /// <returns>0为正常；-1为异常</returns>
        public static extern int FatigueData_LowerLimit(double[] data, int size, double confidenceLevel, double survivalRate, out double lowerLimit);

        [DllImport("MetallicMaterials-FatigueTesting-AnalysisOfData.dll", EntryPoint = "FatigueData_SlgN", CallingConvention = CallingConvention.Cdecl)]
        /// <summary>
        /// 计算S/lgS-lgN的斜率、截距和回归系数 lgN=b-aS(lgS)
        /// </summary>
        /// <param name="x">lgN 数组</param>
        /// <param name="y">S/lgS 数组</param>
        /// <param name="size">数组大小</param>
        /// <param name="slope">斜率(a)</param>
        /// <param name="intercept">截距(b)</param>
        /// <param name="correlationCoefficient">回归系数</param>
        /// <returns></returns>
        public static extern int FatigueDataSlgN(double[] x, double[] y, int size, out double slope, out double intercept, out double correlationCoefficient);

        [DllImport("MetallicMaterials-FatigueTesting-AnalysisOfData.dll", EntryPoint = "FatigueData_SN", CallingConvention = CallingConvention.Cdecl)]
        /// <summary>
        /// 计算S-N的指数和系数 S=aN^b
        /// </summary>
        /// <param name="x">lgN 数组</param>
        /// <param name="y">S/lgS 数组</param>
        /// <param name="size">数组大小</param>
        /// <param name="exponent">指数(b)</param>
        /// <param name="coefficient">系数(a)</param>
        /// <returns>0为正常；-1为异常</returns>
        public static extern int FatigueDataSN(double[] x, double[] y, int size, out double exponent, out double coefficient);

        [DllImport("MetallicMaterials-FatigueTesting-AnalysisOfData.dll", EntryPoint = "FatigueData_S", CallingConvention = CallingConvention.Cdecl)]
        /// <summary>
        /// 升降法计算应力均值
        /// </summary>
        /// <param name="data">数组</param>
        /// <param name="size">数组大小</param>
        /// <param name="mean">均值</param>
        /// <param name="stdDeviation">标准差</param>
        /// <returns>0为正常；-1为异常</returns>
        public static extern int FatigueData_S(double[] data, int size, out double mean, out double stdDeviation);

        public static void TestTwoOutFunc(double[] doubles, out double max, out double min)
        {
            if(doubles != null && doubles.Length>0)
            {
                max = doubles.Max();
                min = doubles.Min();
            } else
            {
                max = 0;
                min = 0;
            }

        }

        /// <summary>
        /// 正弦波峰值到谷值的获取工具函数，在脚本中使用例子
        /// var fuheArray = Model.GetVarByName<BufferInputVar>("input_oneCycleBuffer")["signal_load"];
        /// var bianXingArray = Model.GetVarByName<BufferInputVar>("input_oneCycleBuffer")["signal_ext"];
        /// <summary>
        /// 收集峰值和谷值之间的数据
        /// 使用示例：
        /// double[] out_fuheArray;
        /// double[] out_bianXiangArray;
        /// int collectResult = FuncLibs.FunctionsDynamic.collectValueBetweenPeekAndValley(0, fuheArray, bianXingArray, out out_fuheArray, out out_bianXiangArray);
        /// </summary>
        /// <param name="sampleType">0: 紧凑型; 1: 三点弯</param>
        /// <param name="fuheArray"></param>
        /// <param name="bianXingArray"></param>
        /// <param name="out_fuheArray"></param>
        /// <param name="out_bianXiangArray"></param>
        /// <param name="maxRate">最大值百分比，默认为1.0</param>
        /// <param name="minRate">最小值百分比，默认为1.0</param>
        /// <returns></returns>
        public static int collectValueBetweenPeekAndValley(int sampleType, double[] fuheArray, double[] bianXingArray, out double[] out_fuheArray, out double[] out_bianXiangArray, double maxRate = 1.0, double minRate = 1.0)
        {
            int ret = 0;

            // 初始化输出参数
            out_fuheArray = new double[0];
            out_bianXiangArray = new double[0];

            // 将输入数组转换为List以便使用更多的List功能
            List<double> _loadList = new List<double>(fuheArray);
            List<double> _extensionList = new List<double>(bianXingArray);
            // 找到真正的峰值和谷值索引
            int maxIndex = -1;
            int minIndex = -1;
            double maxValue = 0;
            double minValue = 0; 
            // 对于CT试样，我们需要找到最大值后的第一个最小值
            if (sampleType == 0)
            {
               // 保留三位小数并进1位
                for (int i = 0; i < _loadList.Count; i++)
                {
                    if (i == 0)
                    {
                        minValue = _loadList[i];
                        maxValue = _loadList[i];
                        maxIndex = 0;
                        minIndex = 0;
                    }
                    else
                    {
                        // 如果当前值大于当前最大值，则更新最大值和索引
                        if (_loadList[i] > maxValue)
                        {
                            maxIndex = i;//更新最大值的索引
                            maxValue = _loadList[i]; // 更新最大值
                            minValue = _loadList[i]; // 初始化最小值为当前最大值
                            minIndex = i;// 初始化最小值为当前最大值索引
                        }
                        // 如果当前值小于当前最小值，则更新最小值和索引
                        if (_loadList[i] < minValue)
                        {
                            minValue = _loadList[i];
                            minIndex = i;
                        }
                    }
                }

                //Console.WriteLine("CT试样 - 峰值索引: {0}, 谷值索引: {1}", maxIndex, minIndex);

                // 如果找到了有效的峰谷值索引
                if (maxIndex != -1 && minIndex != -1 && maxIndex < minIndex)
                {
                    // 取峰值到谷值之间的所有数据
                    var tempFuheArray = _loadList.GetRange(maxIndex, minIndex - maxIndex + 1);
                    var tempBianXiangArray = _extensionList.GetRange(maxIndex, minIndex - maxIndex + 1);
                    
                    // 应用过滤条件
                    var filteredData = FilterDataByRates(tempFuheArray.ToArray(), tempBianXiangArray.ToArray(), maxValue, minValue, maxRate, minRate);
                    out_fuheArray = filteredData.Item1;
                    out_bianXiangArray = filteredData.Item2;

                    //Console.WriteLine("成功提取从峰值({0})到谷值({1})的数据，共 {2} 个点", maxValue, minValue, out_fuheArray.Length);
                    //Console.WriteLine("提取数据范围: {0} 到 {1}", out_fuheArray[0], out_fuheArray[out_fuheArray.Length - 1]);
                }
                else
                {
                    //Console.WriteLine("未找到有效的峰谷值索引或峰值在谷值之后");
                    ret = -1;
                }
            }
            // 对于SENB3试样，我们需要找到最小值后的第一个最大值
            else if (sampleType==1)
            {
                for (int i = 0; i < _loadList.Count; i++)
                {
                    if (i == 0)
                    {
                        minValue = _loadList[i];
                        maxValue = _loadList[i];
                        maxIndex = 0;
                        minIndex = 0;
                    }
                    else
                    {
                        // 如果当前值小于当前最小值，则更新最小值和索引
                        if (_loadList[i] < minValue)
                        {
                            minValue = _loadList[i];
                            minIndex = i;
                            maxIndex = i;//更新最大值的索引为当前最小值
                            maxValue = _loadList[i]; // 更新最大值为当前最小值
                        }
                        // 如果当前值大于当前最大值，则更新最大值和索引
                        if (_loadList[i] > maxValue)
                        {
                            maxIndex = i;//更新最大值的索引
                            maxValue = _loadList[i]; // 更新最大值

                        }
                    }
                }

                //Console.WriteLine("SENB3试样 - 谷值索引: {0}, 峰值索引: {1}", minIndex, maxIndex);

                // 如果找到了有效的峰谷值索引
                if (minIndex != -1 && maxIndex != -1 && minIndex < maxIndex)
                {
                    // 取谷值到峰值之间的所有数据
                    var tempFuheArray = _loadList.GetRange(minIndex, maxIndex - minIndex + 1);
                    var tempBianXiangArray = _extensionList.GetRange(minIndex, maxIndex - minIndex + 1);
                    
                    // 应用过滤条件
                    var filteredData = FilterDataByRates(tempFuheArray.ToArray(), tempBianXiangArray.ToArray(), maxValue, minValue, maxRate, minRate);
                    out_fuheArray = filteredData.Item1;
                    out_bianXiangArray = filteredData.Item2;

                    //Console.WriteLine("成功提取从谷值({0})到峰值({1})的数据，共 {2} 个点", minValue, maxValue, out_fuheArray.Length);
                    //Console.WriteLine("提取数据范围: {0} 到 {1}", out_fuheArray[0], out_fuheArray[out_fuheArray.Length - 1]);
                }
                else
                {
                    //Console.WriteLine("未找到有效的峰谷值索引或谷值在峰值之后");
                    ret = -2;
                }
            }

            // 如果没有提取到数据，则返回
            if (out_fuheArray.Length == 0)
            {
                //Console.WriteLine("未能提取到有效数据，返回空数组");
                ret = -3;
            }

            return ret;
        }

        /// <summary>
        /// 根据最大值和最小值百分比过滤数据
        /// </summary>
        /// <param name="fuheArray">负荷数组</param>
        /// <param name="bianXiangArray">变形数组</param>
        /// <param name="maxValue">最大值</param>
        /// <param name="minValue">最小值</param>
        /// <param name="maxRate">最大值百分比</param>
        /// <param name="minRate">最小值百分比</param>
        /// <returns>过滤后的数据元组</returns>
        private static (double[], double[]) FilterDataByRates(double[] fuheArray, double[] bianXiangArray, double maxValue, double minValue, double maxRate, double minRate)
        {
            if (fuheArray.Length != bianXiangArray.Length)
            {
                throw new ArgumentException("fuheArray和bianXiangArray长度必须相等");
            }

            // 计算过滤阈值
            double maxThreshold = maxValue * maxRate;
            double minThreshold = minValue * (1 - minRate);

            List<double> filteredFuheList = new List<double>();
            List<double> filteredBianXiangList = new List<double>();

            for (int i = 0; i < fuheArray.Length; i++)
            {
                double currentValue = fuheArray[i];
                
                // 只保留满足条件的数据：数值小于等于【最大值*maxRate】和数值大于等于【最小值*(1-minRate)】
                if (currentValue <= maxThreshold && currentValue >= minThreshold)
                {
                    filteredFuheList.Add(fuheArray[i]);
                    filteredBianXiangList.Add(bianXiangArray[i]);
                }
            }

            return (filteredFuheList.ToArray(), filteredBianXiangList.ToArray());
        }
    }
}
